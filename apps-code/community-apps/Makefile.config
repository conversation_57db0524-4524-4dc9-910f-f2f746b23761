# context version definition
VERSION := 6.10.0
RELEASE := 3

# log
VERBOSE         := 1
PACKER_LOG      := 0
PACKER_HEADLESS := true

SERVICES := lithops lithops_worker rabbitmq ueransim example phoenixrtos srsRAN openFGS

.DEFAULT_GOAL := help

# default directories
DIR_ONEAPPS := ../one-apps
DIR_BUILD   := build
DIR_EXPORT  := export
$(shell mkdir -p $(DIR_BUILD) $(DIR_EXPORT))

# don't delete exported
.SECONDARY: $(SERVICES:%=$(DIR_EXPORT)/%.qcow2)

# logging func
INFO=sh -c 'if [ $(VERBOSE) = 1 ]; then echo [INFO] $$1; fi' INFO

# export all variables
export
