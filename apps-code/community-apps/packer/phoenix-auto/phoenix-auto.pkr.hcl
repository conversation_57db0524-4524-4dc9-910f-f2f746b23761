#include "common.pkr.hcl"

source "null" "create_iso" {
  communicator = "none"
}

build {
  name    = "create_iso"
  sources = ["source.null.create_iso"]

  provisioner "shell-local" {
    inline = [
      "cloud-localds ${var.input_dir}/phoenix-auto-cloud-init.iso ${var.input_dir}/cloud-init.yml"
    ]
  }
}

locals {
  disk_size = 8192
}

source "qemu" "phoenix-auto" {
  cpus             = 2
  memory           = 2048
  accelerator      = "kvm"

  iso_url          = var.ubuntu["2204"].iso_url
  iso_checksum     = var.ubuntu["2204"].iso_checksum

  headless         = var.headless
  disk_image       = true
  disk_cache       = "unsafe"
  disk_interface   = "virtio"
  net_device       = "virtio-net"
  format           = "qcow2"
  disk_size        = local.disk_size
  output_directory = var.output_dir

  qemuargs = [
    ["-cdrom", "${var.input_dir}/phoenix-auto-cloud-init.iso"],
    ["-serial", "stdio"],
  ]

  ssh_username     = "root"
  ssh_password     = "opennebula"
  ssh_timeout      = "900s"
  shutdown_command = "poweroff"
  vm_name          = "phoenix-auto"
}

build {
  name    = "phoenix-auto"
  sources = ["source.qemu.phoenix-auto"]

  provisioner "shell" {
    inline = [
      "set -eux",
      
      # Prevent initramfs updates during build
      "echo 'exit 0' > /usr/sbin/update-initramfs",
      "chmod +x /usr/sbin/update-initramfs",

      # Basic setup
      "apt-get update -y",
      "DEBIAN_FRONTEND=noninteractive apt-get purge -y cloud-init cloud-initramfs-growroot || true",

      # Install essentials
      "apt-get install -y --no-install-recommends wget curl ca-certificates",
      
      # Install one-context
      "wget -qO /tmp/one-context.deb https://github.com/OpenNebula/one-apps/releases/download/v6.10.0-3/one-context_6.10.0-3.deb",
      "dpkg -i /tmp/one-context.deb || apt-get install -fy --no-install-recommends",
      "rm -f /tmp/one-context.deb",
      "systemctl enable one-context.service",

      # Pre-configure Docker repository
      "apt-get install -y --no-install-recommends gnupg",
      "install -d -m0755 /etc/apt/keyrings",
      "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg",
      "chmod a+r /etc/apt/keyrings/docker.gpg",
      "echo \"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu jammy stable\" | tee /etc/apt/sources.list.d/docker.list",
      "apt-get update -y",

      # Configure auto-login
      "mkdir -p /etc/systemd/system/<EMAIL>.d",
      "cat <<'CONF' >/etc/systemd/system/<EMAIL>.d/autologin.conf\n[Service]\nExecStart=\nExecStart=-/sbin/agetty --autologin root --noclear %I linux\nCONF",
      "systemctl daemon-reload",
      "<NAME_EMAIL> || true",

      "echo 'Docker VM base setup complete'"
    ]
  }

  provisioner "file" {
    source      = "82-configure-context.sh"
    destination = "/tmp/82-configure-context.sh"
  }

  provisioner "shell" {
    execute_command = "sudo -iu root {{.Vars}} bash {{.Path}}"
    scripts = ["/tmp/82-configure-context.sh"]
    expect_disconnect = true
  }

  provisioner "shell" {
    inline = [
      "apt-get autoremove -y",
      "apt-get autoclean", 
      "rm -rf /var/lib/apt/lists/*",
      "rm -rf /tmp/* /var/tmp/*",
      "history -c",
      "sync"
    ]
  }
}
