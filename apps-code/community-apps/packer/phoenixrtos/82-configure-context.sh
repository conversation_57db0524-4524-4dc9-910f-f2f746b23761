#!/usr/bin/env bash

# Configure and enable service context.

exec 1>&2
set -eux -o pipefail

if [ -f /etc/one-appliance/net-90-service-appliance ]; then
    mv /etc/one-appliance/net-90-service-appliance /etc/one-context.d/
fi
if [ -f /etc/one-appliance/net-99-report-ready ]; then
    mv /etc/one-appliance/net-99-report-ready /etc/one-context.d/
fi

chown root:root /etc/one-context.d/*
chmod u=rwx,go=rx /etc/one-context.d/*

sync
