source "null" "create_iso" {
  communicator = "none"
}

build {
  name    = "create_iso"
  sources = ["source.null.create_iso"]

  provisioner "shell-local" {
    inline = [
      "cloud-localds ${var.input_dir}/${var.appliance_name}-cloud-init.iso ${var.input_dir}/cloud-init.yml"
    ]
  }
}

# A Virtual Machine is created with qemu in order to run the setup from the ISO on the CD-ROM
# Here are the details about the VM virtual hardware
source "qemu" "docker-phoenix" {
  cpus             = 2
  cpu_model        = "host"
  memory           = 2048
  accelerator      = "kvm"

  # Use Ubuntu 22.04 LTS cloud image as base
  iso_url          = "https://cloud-images.ubuntu.com/jammy/current/jammy-server-cloudimg-amd64.img"
  iso_checksum     = "none"

  headless         = var.headless
  disk_image       = true
  disk_cache       = "unsafe"
  disk_interface   = "virtio"
  net_device       = "virtio-net"
  format           = "qcow2"
  skip_resize_disk = false
  disk_size        = 8192
  output_directory = var.output_dir

  qemuargs = [
    ["-cdrom", "${var.input_dir}/${var.appliance_name}-cloud-init.iso"],
    ["-serial", "stdio"],
  ]

  ssh_username     = "root"
  ssh_password     = "opennebula"
  ssh_timeout      = "900s"
  shutdown_command = "poweroff"
  vm_name          = var.appliance_name
}

# Once the VM launches the following logic will be executed inside it to customize what happens inside
# Essentially, a bunch of scripts are pulled from ./appliances and placed inside the Guest OS
# There are shared libraries for ruby and bash. Bash is used in this example
build {
  sources = ["source.qemu.docker-phoenix"]

  # revert insecure ssh options done by context start_script
  provisioner "shell" {
    scripts = ["${var.input_dir}/81-configure-ssh.sh"]
  }

  ##############################################
  # BEGIN placing script logic inside Guest OS #
  ##############################################

  # Install Docker and pre-pull Phoenix RTOS image during build time
  provisioner "shell" {
    inline = [
      "set -eux",

      # Force IPv4 to avoid IPv6 timeouts in CI
      "printf 'Acquire::ForceIPv4 \"true\";\\n' > /etc/apt/apt.conf.d/99force-ipv4",

      # Remove any cloud-init & its growroot hook (clean slate)
      "apt-get update -y",
      "DEBIAN_FRONTEND=noninteractive apt-get purge -y cloud-init cloud-initramfs-growroot || true",

      # Install one-context and its dependencies
      "apt-get install -y wget cloud-image-utils cloud-utils ruby qemu-guest-agent virt-what",
      "wget -qO /tmp/one-context.deb https://github.com/OpenNebula/one-apps/releases/download/v6.10.0-3/one-context_6.10.0-3.deb",
      "dpkg -i /tmp/one-context.deb || apt-get install -fy",
      "rm -f /tmp/one-context.deb",
      "systemctl enable one-context.service",

      # Install Docker prerequisites
      "apt-get update -y",
      "apt-get install -y ca-certificates curl gnupg lsb-release",

      # Add Docker's official GPG key
      "install -d -m0755 /etc/apt/keyrings",
      "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg",
      "chmod a+r /etc/apt/keyrings/docker.gpg",

      # Configure Docker APT repo
      "echo \"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu jammy stable\" | tee /etc/apt/sources.list.d/docker.list",

      # Install Docker engine
      "apt-get update -y",
      "DEBIAN_FRONTEND=noninteractive apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin",
      "systemctl enable --now docker",

      # Pre-pull Phoenix RTOS container so QCOW2 already contains it
      "docker pull pablodelarco/phoenix-rtos-one:latest"
    ]
  }

  provisioner "shell" {
    inline_shebang = "/bin/bash -e"
    inline = [
      "install -o 0 -g 0 -m u=rwx,g=rx,o=   -d /etc/one-appliance/{,service.d/,lib/}",
      "install -o 0 -g 0 -m u=rwx,g=rx,o=rx -d /opt/one-appliance/{,bin/}",
    ]
  }

  provisioner "file" {
    sources = [
      "../one-apps/appliances/scripts/net-90-service-appliance",
      "../one-apps/appliances/scripts/net-99-report-ready",
    ]
    destination = "/etc/one-appliance/"
  }

  # Bash libraries at appliances/lib for easier custom implementation in bash logic
  provisioner "file" {
    sources = [
      "../../lib/common.sh",
      "../../lib/functions.sh",
    ]
    destination = "/etc/one-appliance/lib/"
  }

  # Contains the appliance service management tool
  # https://github.com/OpenNebula/one-apps/wiki/apps_intro#appliance-life-cycle
  provisioner "file" {
    source      = "../one-apps/appliances/service.sh"
    destination = "/etc/one-appliance/service"
  }
  # Pull your own custom logic here. Must be called appliance.sh if using bash tools
  provisioner "file" {
    sources     = ["../../appliances/docker-phoenix/appliance.sh"]
    destination = "/etc/one-appliance/service.d/"
  }

  provisioner "shell" {
    scripts = ["${var.input_dir}/82-configure-context.sh"]
  }

  #######################################################################
  # Setup appliance: Execute install step                               #
  # https://github.com/OpenNebula/one-apps/wiki/apps_intro#installation #
  #######################################################################
  provisioner "shell" {
    inline_shebang = "/bin/bash -e"
    inline         = ["/etc/one-appliance/service install && sync"]
  }

  # Remove machine ID from the VM and get it ready for continuous cloud use
  # https://github.com/OpenNebula/one-apps/wiki/tool_dev#appliance-build-process
  post-processor "shell-local" {
    execute_command = ["bash", "-c", "{{.Vars}} {{.Script}}"]
    environment_vars = [
      "OUTPUT_DIR=${var.output_dir}",
      "APPLIANCE_NAME=${var.appliance_name}",
    ]
    scripts = ["packer/postprocess.sh"]
  }
}
