#!/bin/bash

SCRIPT=$(cat <<MAINEND
gawk -i inplace -f- /etc/ssh/sshd_config <<'EOF'
BEGIN { update = "PasswordAuthentication yes" }
/^[#\s]*PasswordAuthentication\s*/ { \$0 = update; found = 1 }
{ print }
END { if (!found) print update >> FILENAME }
EOF

gawk -i inplace -f- /etc/ssh/sshd_config <<'EOF'
BEGIN { update = "PermitRootLogin yes" }
/^[#\s]*PermitRootLogin\s*/ { \$0 = update; found = 1 }
{ print }
END { if (!found) print update >> FILENAME }
EOF

systemctl reload sshd

echo "nameserver *******" > /etc/resolv.conf
MAINEND
)

cat<<EOF
ETH0_METHOD='dhcp'
NETWORK='YES'
SET_HOSTNAME='openfgs'
PASSWORD='opennebula'
ETH0_MAC='00:11:22:33:44:55'
START_SCRIPT_BASE64="$(echo "$SCRIPT" | base64 -w0)"
EOF