popularity-contest popularity-contest/participate boolean false

d-i debian-installer/language string en
d-i debian-installer/locale string en_US
d-i debian-installer/country string US

d-i keyboard-configuration/xkb-keymap select us

d-i netcfg/choose_interface select auto
d-i netcfg/wireless_wep string

d-i clock-setup/utc boolean true
d-i time/zone string US/Eastern
d-i clock-setup/ntp boolean true

d-i partman/confirm boolean true
d-i partman/confirm_nooverwrite boolean true
d-i partman/choose_partition select finish
d-i partman-basicfilesystems/no_swap boolean false
d-i partman-auto/method string regular
d-i partman-auto/disk string /dev/sda
d-i partman-auto/choose_recipe select root-only
d-i partman-auto/expert_recipe string    \
root-only :: 2048 50 -1 ext4             \
    $primary{ } $bootable{ }             \
    method{ format } format{ }           \
    use_filesystem{ } filesystem{ ext4 } \
    mountpoint{ / }                      \
    .

d-i apt-setup/cdrom/set-first boolean false
d-i apt-setup/disable-cdrom-entries boolean true
d-i apt-setup/use_mirror boolean false

d-i pkgsel/run_tasksel boolean false
d-i pkgsel/include string openssh-server
d-i pkgsel/upgrade select safe-upgrade

d-i passwd/make-user boolean false
d-i passwd/root-login boolean true
d-i passwd/root-password password opennebula
d-i passwd/root-password-again password opennebula

d-i preseed/late_command string \
in-target sed -i 's/^#PermitRootLogin .*/PermitRootLogin yes/' /etc/ssh/sshd_config

d-i choose-init/select_init select sysvinit
d-i choose-init/selected_sysvinit bool true

d-i grub-installer/only_debian boolean true
d-i grub-installer/bootdev string /dev/sda

d-i finish-install/reboot_in_progress note
