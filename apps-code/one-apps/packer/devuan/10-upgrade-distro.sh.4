#!/usr/bin/env bash

# Install required packages and upgrade the distro.

[[ -e /usr/sbin/policy-rc.d ]] && cp /usr/sbin/policy-rc.d /tmp/
policy_rc_d_disable() (echo "exit 101" >/usr/sbin/policy-rc.d && chmod a+x /usr/sbin/policy-rc.d)
policy_rc_d_enable()  (echo "exit 0"   >/usr/sbin/policy-rc.d && chmod a+x /usr/sbin/policy-rc.d)

exec 1>&2
set -eux -o pipefail

export DEBIAN_FRONTEND=noninteractive

sed -i '/^deb cdrom/d' /etc/apt/sources.list
echo "deb http://deb.devuan.org/merged chimaera          main" >> /etc/apt/sources.list
echo "deb http://deb.devuan.org/merged chimaera-updates  main" >> /etc/apt/sources.list
echo "deb http://deb.devuan.org/merged chimaera-security main" >> /etc/apt/sources.list

apt-get update -y

policy_rc_d_disable

debconf-set-selections <<< 'grub-pc grub-pc/install_devices multiselect /dev/sda'

apt-get install -y --fix-broken

apt-get upgrade -y -o Dpkg::Options::="--force-confdef" -o Dpkg::Options::="--force-confold"

# Ensure packages needed for post-processing scripts do exist.
apt-get install -y curl gawk grep jq

policy_rc_d_enable

ln -sf /usr/share/zoneinfo/UTC /etc/localtime

sync
