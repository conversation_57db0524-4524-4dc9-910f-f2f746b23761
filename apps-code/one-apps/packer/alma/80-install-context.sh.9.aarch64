#!/usr/bin/env bash

# Download and install the latest one-context package.

: "${CTXEXT:=el9.noarch.rpm}"

exec 1>&2
set -eux -o pipefail

LATEST=$(find /context/ -type f -name "one-context*.$CTXEXT" | sort -V | tail -n1)

dnf install -y "$LATEST" haveged

systemctl enable haveged

# >>> Apply only on one-context >= 6.1 >>>
if ! rpm -q --queryformat '%{VERSION}' one-context | grep -E '^([1-5]\.|6\.0\.)'; then
    dnf install -y --setopt=install_weak_deps=False NetworkManager systemd-networkd
fi
# <<< Apply only on one-context >= 6.1 <<<

sync
