#!/usr/bin/env bash

: "${ENV_FILE:=/var/run/one-context/one_env}"

set -o errexit

if [[ "$REPORT_READY" != "YES" ]]; then
    exit
fi

# $TOKENTXT is available only through the env. file
if [[ -f "${ENV_FILE}" ]]; then
    . "${ENV_FILE}"
fi

# Reports only if ONE service appliance bootstrapped successfully
if [[ -x '/etc/one-appliance/service' ]]; then
    _STATUS=$(cat '/etc/one-appliance/status' 2>/dev/null)
    if [[ "$_STATUS" != 'bootstrap_success' ]]; then
        exit
    fi
fi

if ! command -v onegate ; then
    echo "ERROR: No way to signal READY=YES (onegate binary not found)" >&2
    exit 1
fi > /dev/null # this will not drop the error message which goes to stderr

if onegate vm update --data READY=YES; then
    echo "Reported READY"
    exit
fi

echo "ERROR: Failed to report READY" >&2
exit 1
