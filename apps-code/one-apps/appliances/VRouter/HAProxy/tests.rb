# frozen_string_literal: true

require 'json'
require 'rspec'
require 'tmpdir'

def clear_env
    ENV.delete_if { |name| name.start_with?('ETH') || name.include?('VROUTER_') || name.include?('_VNF_') }
end

def clear_vars(object)
    object.instance_variables.each { |name| object.remove_instance_variable(name) }
end

RSpec.describe self do
    it 'should provide and parse all env vars (static)' do
        clear_env

        ENV['ONEAPP_VNF_HAPROXY_ENABLED'] = 'YES'

        ENV['ONEAPP_VNF_HAPROXY_REFRESH_RATE'] = ''

        ENV['ONEAPP_VNF_HAPROXY_LB0_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_PORT'] = '1234'

        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_PORT'] = '12345'

        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER1_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER1_PORT'] = '12345'

        ENV['ONEAPP_VNF_HAPROXY_LB1_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_PORT'] = '4321'

        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER0_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER0_PORT'] = '54321'

        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER1_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER1_PORT'] = '54321'

        ENV['ONEAPP_VNF_LB_ONEGATE_API'] = ''

        load './main.rb'; include Service::HAProxy

        expect(Service::HAProxy::ONEAPP_VNF_HAPROXY_ENABLED).to be true
        expect(Service::HAProxy::ONEAPP_VNF_HAPROXY_REFRESH_RATE).to eq '30'
        expect(Service::HAProxy::ONEAPP_VNF_LB_ONEGATE_API).to eq 'auto'

        Service::HAProxy.const_set :VROUTER_ID, '86'

        allow(Service::HAProxy).to receive(:detect_nics).and_return(%w[eth0 eth1 eth2 eth3])

        expect(Service::HAProxy.extract_backends).to eq({
            by_endpoint: {
                [ 0, '**********', '1234' ] =>
                    { [ '***********', '12345' ] => { host: '***********', port: '12345' },
                      [ '***********', '12345' ] => { host: '***********', port: '12345' } },

                [ 1, '**********', '4321' ] =>
                    { [ '***********', '54321' ] => { host: '***********', port: '54321' },
                      [ '***********', '54321' ] => { host: '***********', port: '54321' } } },

            options: { 0 => { ip: '**********', port: '1234' },
                       1 => { ip: '**********', port: '4321' } }
        })
    end

    it 'should render servers.cfg (static)' do
        clear_env

        ENV['ONEAPP_VNF_HAPROXY_ENABLED'] = 'YES'

        ENV['ONEAPP_VNF_HAPROXY_REFRESH_RATE'] = ''

        ENV['ONEAPP_VNF_HAPROXY_LB0_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_PORT'] = '1234'

        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_PORT'] = '12345'

        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER1_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER1_PORT'] = '12345'

        ENV['ONEAPP_VNF_HAPROXY_LB1_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_PORT'] = '4321'

        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER0_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER0_PORT'] = '54321'

        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER1_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_SERVER1_PORT'] = '54321'

        load './main.rb'; include Service::HAProxy

        Service::HAProxy.const_set :VROUTER_ID, '86'

        allow(Service::HAProxy).to receive(:toggle).and_return(nil)
        allow(Service::HAProxy).to receive(:sleep).and_return(nil)
        allow(Service::HAProxy).to receive(:detect_nics).and_return(%w[eth0 eth1 eth2 eth3])
        allow(Service::HAProxy).to receive(:addrs_to_nics).and_return({
            '**********' => ['eth0']
        })

        clear_vars Service::HAProxy

        output = <<~STATIC
            frontend lb0_1234
                mode tcp
                bind **********:1234
                default_backend lb0_1234

            backend lb0_1234
                mode tcp
                balance roundrobin
                option tcp-check
                server lb0_***********_12345 ***********:12345 check observe layer4 error-limit 50 on-error mark-down
                server lb0_***********_12345 ***********:12345 check observe layer4 error-limit 50 on-error mark-down

            frontend lb1_4321
                mode tcp
                bind **********:4321
                default_backend lb1_4321

            backend lb1_4321
                mode tcp
                balance roundrobin
                option tcp-check
                server lb1_***********_54321 ***********:54321 check observe layer4 error-limit 50 on-error mark-down
                server lb1_***********_54321 ***********:54321 check observe layer4 error-limit 50 on-error mark-down
        STATIC

        Dir.mktmpdir do |dir|
            Service::HAProxy.execute basedir: dir
            result = File.read "#{dir}/servers.cfg"
            expect(result.strip).to eq output.strip
        end
    end

    it 'should render servers.cfg (dynamic)' do
        clear_env

        ENV['ONEAPP_VNF_HAPROXY_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_HAPROXY_ONEGATE_ENABLED'] = 'YES'

        ENV['ONEAPP_VNF_HAPROXY_REFRESH_RATE'] = ''

        ENV['ONEAPP_VNF_HAPROXY_LB0_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_PORT'] = '6969'

        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_PORT'] = '1234'

        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER1_HOST'] = '***********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER1_PORT'] = '1234'

        ENV['ONEAPP_VNF_HAPROXY_LB1_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_PORT'] = '8686'

        # Internally forced to 'VROUTER' since not in OneFlow
        ENV['ONEAPP_VNF_LB_ONEGATE_API'] = 'service'

        (vnets ||= []) << JSON.parse(<<~'VNET0')
            {
              "VNET": {
                "ID": "0",
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "LEASES": {
                        "LEASE": [
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:ca",
                            "VM": "167",
                            "NIC_NAME": "NIC0",
                            "BACKEND": "YES",

                            "ONEGATE_HAPROXY_LB0_IP": "**********",
                            "ONEGATE_HAPROXY_LB0_PORT": "6969",
                            "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                            "ONEGATE_HAPROXY_LB0_SERVER_PORT": "1234",
                            "ONEGATE_HAPROXY_LB0_SERVER_WEIGHT": "1"
                          },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:c9",
                            "VM": "167",
                            "NIC_NAME": "NIC0",
                            "BACKEND": "YES",

                            "ONEGATE_HAPROXY_LB0_IP": "**********",
                            "ONEGATE_HAPROXY_LB0_PORT": "6969",
                            "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                            "ONEGATE_HAPROXY_LB0_SERVER_PORT": "1234",
                            "ONEGATE_HAPROXY_LB0_SERVER_WEIGHT": "1",

                            "ONEGATE_HAPROXY_LB1_ID": "NOT-86",
                            "ONEGATE_HAPROXY_LB1_IP": "**********",
                            "ONEGATE_HAPROXY_LB1_PORT": "8686",
                            "ONEGATE_HAPROXY_LB1_SERVER_HOST": "***********",
                            "ONEGATE_HAPROXY_LB1_SERVER_PORT": "4321",
                            "ONEGATE_HAPROXY_LB1_SERVER_WEIGHT": "1"
                          },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:c8",
                            "VM": "167",
                            "NIC_NAME": "NIC0",
                            "BACKEND": "YES",

                            "ONEGATE_HAPROXY_LB0_IP": "**********",
                            "ONEGATE_HAPROXY_LB0_PORT": "6969",
                            "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                            "ONEGATE_HAPROXY_LB0_SERVER_PORT": "1234",
                            "ONEGATE_HAPROXY_LB0_SERVER_WEIGHT": "1",

                            "ONEGATE_HAPROXY_LB1_ID": "86",
                            "ONEGATE_HAPROXY_LB1_IP": "**********",
                            "ONEGATE_HAPROXY_LB1_PORT": "8686",
                            "ONEGATE_HAPROXY_LB1_SERVER_HOST": "***********",
                            "ONEGATE_HAPROXY_LB1_SERVER_PORT": "4321",
                            "ONEGATE_HAPROXY_LB1_SERVER_WEIGHT": "1"
                          }
                        ]
                      }
                    }
                  ]
                }
              }
            }
        VNET0

        load './main.rb'; include Service::HAProxy

        Service::HAProxy.const_set :VROUTER_ID, '86'

        allow(Service::HAProxy).to receive(:detect_nics).and_return(%w[eth0 eth1 eth2 eth3])
        allow(Service::HAProxy).to receive(:addrs_to_nics).and_return({
            '**********' => ['eth0']
        })

        clear_vars Service::HAProxy

        output = <<~'DYNAMIC'
            frontend lb0_6969
                mode tcp
                bind **********:6969
                default_backend lb0_6969

            backend lb0_6969
                mode tcp
                balance roundrobin
                option tcp-check
                server lb0_***********_1234 ***********:1234 check observe layer4 error-limit 50 on-error mark-down
                server lb0_***********_1234 ***********:1234 check observe layer4 error-limit 50 on-error mark-down
                server lb0_***********_1234 ***********:1234 check observe layer4 error-limit 50 on-error mark-down

            frontend lb1_8686
                mode tcp
                bind **********:8686
                default_backend lb1_8686

            backend lb1_8686
                mode tcp
                balance roundrobin
                option tcp-check
                server lb1_***********_4321 ***********:4321 check observe layer4 error-limit 50 on-error mark-down
        DYNAMIC

        Dir.mktmpdir do |dir|
            haproxy_vars = Service::HAProxy.extract_backends vnets
            Service::HAProxy.render_servers_cfg haproxy_vars, basedir: dir
            result = File.read "#{dir}/servers.cfg"
            expect(result.strip).to eq output.strip
        end
    end

    it 'should render servers.cfg (dynamic/OneFlow)' do
        clear_env

        ENV['ONEAPP_VNF_HAPROXY_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_HAPROXY_ONEGATE_ENABLED'] = 'YES'

        ENV['ONEAPP_VNF_HAPROXY_REFRESH_RATE'] = ''

        ENV['ONEAPP_VNF_HAPROXY_LB0_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB0_PORT'] = '5432'

        ENV['ONEAPP_VNF_HAPROXY_LB1_IP'] = '**********'
        ENV['ONEAPP_VNF_HAPROXY_LB1_PORT'] = '4321'

        ENV['ONEAPP_VNF_LB_ONEGATE_API'] = 'auto'
        ENV['SERVICE_ID'] = '123'

        (vms ||= []) << JSON.parse(<<~'VM0')
            {
              "VM": {
                "NAME": "server_0_(service_23)",
                "ID": "435",
                "STATE": "3",
                "LCM_STATE": "3",
                "USER_TEMPLATE": {
                  "HOT_RESIZE": {
                    "CPU_HOT_ADD_ENABLED": "NO",
                    "MEMORY_HOT_ADD_ENABLED": "NO"
                  },
                  "LOGO": "images/logos/linux.png",
                  "LXD_SECURITY_PRIVILEGED": "true",
                  "MEMORY_UNIT_COST": "MB",

                  "ONEGATE_HAPROXY_LB0_IP": "**********",
                  "ONEGATE_HAPROXY_LB0_PORT": "5432",
                  "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                  "ONEGATE_HAPROXY_LB0_SERVER_PORT": "2345",

                  "ONEGATE_HAPROXY_LB1_ID": "86",
                  "ONEGATE_HAPROXY_LB1_IP": "**********",
                  "ONEGATE_HAPROXY_LB1_PORT": "4321",
                  "ONEGATE_HAPROXY_LB1_SERVER_HOST": "***********",
                  "ONEGATE_HAPROXY_LB1_SERVER_PORT": "1234",

                  "ROLE_NAME": "server",
                  "SERVICE_ID": "23"
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:ca",
                      "NAME": "_NIC0",
                      "NETWORK": "service"
                    },
                    {
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:7a",
                      "NAME": "_NIC1",
                      "NETWORK": "private"
                    }
                  ],
                  "NIC_ALIAS": []
                }
              }
            }
        VM0
        (vms ||= []) << JSON.parse(<<~'VM1')
            {
              "VM": {
                "NAME": "server_1_(service_23)",
                "ID": "436",
                "STATE": "3",
                "LCM_STATE": "3",
                "USER_TEMPLATE": {
                  "HOT_RESIZE": {
                    "CPU_HOT_ADD_ENABLED": "NO",
                    "MEMORY_HOT_ADD_ENABLED": "NO"
                  },
                  "LOGO": "images/logos/linux.png",
                  "LXD_SECURITY_PRIVILEGED": "true",
                  "MEMORY_UNIT_COST": "MB",

                  "ONEGATE_HAPROXY_LB0_IP": "**********",
                  "ONEGATE_HAPROXY_LB0_PORT": "5432",
                  "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                  "ONEGATE_HAPROXY_LB0_SERVER_PORT": "2345",

                  "ONEGATE_HAPROXY_LB1_ID": "123",
                  "ONEGATE_HAPROXY_LB1_IP": "**********",
                  "ONEGATE_HAPROXY_LB1_PORT": "4321",
                  "ONEGATE_HAPROXY_LB1_SERVER_HOST": "***********",
                  "ONEGATE_HAPROXY_LB1_SERVER_PORT": "1234",

                  "ROLE_NAME": "server",
                  "SERVICE_ID": "23"
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:cb",
                      "NAME": "_NIC0",
                      "NETWORK": "service"
                    },
                    {
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:7b",
                      "NAME": "_NIC1",
                      "NETWORK": "private"
                    }
                  ],
                  "NIC_ALIAS": []
                }
              }
            }
        VM1

        load './main.rb'; include Service::HAProxy

        Service::HAProxy.const_set :VROUTER_ID, nil
        Service::HAProxy.const_set :SERVICE_ID, '123'

        allow(Service::HAProxy).to receive(:detect_nics).and_return(%w[eth0 eth1 eth2 eth3])
        allow(Service::HAProxy).to receive(:addrs_to_nics).and_return({
            '**********' => ['eth0']
        })

        clear_vars Service::HAProxy

        output = <<~'DYNAMIC'
            frontend lb0_5432
                mode tcp
                bind **********:5432
                default_backend lb0_5432

            backend lb0_5432
                mode tcp
                balance roundrobin
                option tcp-check
                server lb0_***********_2345 ***********:2345 check observe layer4 error-limit 50 on-error mark-down
                server lb0_***********_2345 ***********:2345 check observe layer4 error-limit 50 on-error mark-down

            frontend lb1_4321
                mode tcp
                bind **********:4321
                default_backend lb1_4321

            backend lb1_4321
                mode tcp
                balance roundrobin
                option tcp-check
                server lb1_***********_1234 ***********:1234 check observe layer4 error-limit 50 on-error mark-down
        DYNAMIC

        Dir.mktmpdir do |dir|
            haproxy_vars = Service::HAProxy.extract_backends vms
            Service::HAProxy.render_servers_cfg haproxy_vars, basedir: dir
            result = File.read "#{dir}/servers.cfg"
            expect(result.strip).to eq output.strip
        end
    end

  it 'should render servers.cfg using VR API (dynamic/OneFlow)' do
      clear_env

      ENV['ONEAPP_VNF_HAPROXY_ENABLED'] = 'YES'
      ENV['ONEAPP_VNF_HAPROXY_ONEGATE_ENABLED'] = 'YES'

      ENV['ONEAPP_VNF_HAPROXY_REFRESH_RATE'] = ''

      ENV['ONEAPP_VNF_HAPROXY_LB0_IP'] = '**********'
      ENV['ONEAPP_VNF_HAPROXY_LB0_PORT'] = '6969'

      ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_HOST'] = '***********'
      ENV['ONEAPP_VNF_HAPROXY_LB0_SERVER0_PORT'] = '1234'

      ENV['ONEAPP_VNF_LB_ONEGATE_API'] = 'vrouter'

      (vnets ||= []) << JSON.parse(<<~'VNET0')
          {
            "VNET": {
              "ID": "0",
              "AR_POOL": {
                "AR": [
                  {
                    "AR_ID": "0",
                    "LEASES": {
                      "LEASE": [
                        {
                          "IP": "***********",
                          "MAC": "02:00:0a:02:0b:ca",
                          "VM": "167",
                          "NIC_NAME": "NIC0",
                          "BACKEND": "YES",

                          "ONEGATE_HAPROXY_LB0_IP": "**********",
                          "ONEGATE_HAPROXY_LB0_PORT": "6969",
                          "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                          "ONEGATE_HAPROXY_LB0_SERVER_PORT": "1234",
                          "ONEGATE_HAPROXY_LB0_SERVER_WEIGHT": "1"
                        },
                        {
                          "IP": "***********",
                          "MAC": "02:00:0a:02:0b:c8",
                          "VM": "167",
                          "NIC_NAME": "NIC0",
                          "BACKEND": "YES",

                          "ONEGATE_HAPROXY_LB0_IP": "**********",
                          "ONEGATE_HAPROXY_LB0_PORT": "6969",
                          "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                          "ONEGATE_HAPROXY_LB0_SERVER_PORT": "1234",
                          "ONEGATE_HAPROXY_LB0_SERVER_WEIGHT": "1"
                        }
                      ]
                    }
                  }
                ]
              }
            }
          }
      VNET0

      load './main.rb'; include Service::HAProxy

      Service::HAProxy.const_set :VROUTER_ID, '87'
      Service::HAProxy.const_set :SERVICE_ID, '124'

      allow(Service::HAProxy).to receive(:detect_nics).and_return(%w[eth0 eth1 eth2 eth3])
      allow(Service::HAProxy).to receive(:addrs_to_nics).and_return({
          '**********' => ['eth0']
      })

      clear_vars Service::HAProxy

      output = <<~'DYNAMIC'
          frontend lb0_6969
              mode tcp
              bind **********:6969
              default_backend lb0_6969

          backend lb0_6969
              mode tcp
              balance roundrobin
              option tcp-check
              server lb0_***********_1234 ***********:1234 check observe layer4 error-limit 50 on-error mark-down
              server lb0_***********_1234 ***********:1234 check observe layer4 error-limit 50 on-error mark-down
      DYNAMIC

      Dir.mktmpdir do |dir|
          haproxy_vars = Service::HAProxy.extract_backends vnets
          Service::HAProxy.render_servers_cfg haproxy_vars, basedir: dir
          result = File.read "#{dir}/servers.cfg"
          puts "#{output.strip}"
          expect(result.strip).to eq output.strip
      end
  end
end
