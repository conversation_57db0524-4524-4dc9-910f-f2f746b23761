# frozen_string_literal: true

require 'yaml'
require 'rspec'
require 'tmpdir'

def clear_env
    ENV.delete_if { |name| name.start_with?('ETH') || name.include?('VROUTER_') || name.include?('_VNF_') }
end

def clear_vars(object)
    object.instance_variables.each { |name| object.remove_instance_variable(name) }
end

RSpec.describe self do
    it 'should provide and parse all env vars' do
        clear_env

        ENV['ONEAPP_VNF_DHCP4_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_DHCP4_AUTHORITATIVE'] = 'YES'

        ENV['ONEAPP_VNF_DHCP4_MAC2IP_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_DHCP4_MAC2IP_MACPREFIX'] = '02:00'

        ENV['ONEAPP_VNF_DHCP4_LEASE_TIME'] = '3600'

        ENV['ONEAPP_VNF_DHCP4_GATEWAY'] = '*******'
        ENV['ONEAPP_VNF_DHCP4_DNS'] = '*******'

        ENV['ONEAPP_VNF_DHCP4_INTERFACES'] = 'eth0 eth1 eth2 eth3'
        ENV['ETH0_VROUTER_MANAGEMENT'] = 'YES'

        ENV['ONEAPP_VNF_DHCP4_ETH2'] = '30.0.0.0/8:***********-***********'
        ENV['ONEAPP_VNF_DHCP4_ETH2_GATEWAY'] = '**********'
        ENV['ONEAPP_VNF_DHCP4_ETH2_DNS'] = '*******'

        ENV['ONEAPP_VNF_DHCP4_ETH3_GATEWAY'] = '**********'
        ENV['ONEAPP_VNF_DHCP4_ETH3_DNS'] = '*******'

        ENV['ETH0_IP'] = '***********'
        ENV['ETH0_MASK'] = '*************'

        ENV['ETH1_IP'] = '***********'
        ENV['ETH1_MASK'] = '***********'

        ENV['ETH1_ALIAS0_IP'] = '*******' # ignored (unsupported)
        ENV['ETH1_ALIAS0_MASK'] = '*************' # ignored (unsupported)

        ENV['ETH2_IP'] = '***********'
        ENV['ETH2_MASK'] = '*********'

        ENV['ETH3_IP'] = '***********'
        ENV['ETH3_MASK'] = '*************'

        ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '***********'
        ENV['ONEAPP_VROUTER_ETH1_VIP1'] = '*******' # ignored (not in the subnet)

        load './main.rb'; include Service::DHCP4v2

        allow(Service::DHCP4v2).to receive(:ip_link_show).and_return(
            { 'mtu' => 1111 },
            { 'mtu' => 2222 },
            { 'mtu' => 3333 }
        )

        clear_vars Service::DHCP4v2

        expect(Service::DHCP4v2.parse_env).to eq ({
            'eth1' => [ { address: '***********',
                          dns:     '*******',
                          gateway: '*******',
                          mtu:     1111,
                          range:   '*********-*************',
                          subnet:  '*********/16',
                          vips:    %w[***********] } ],

            'eth2' => [ { address: '***********',
                          dns:     '*******',
                          gateway: '**********',
                          mtu:     2222,
                          range:   '***********-***********',
                          subnet:  '30.0.0.0/8',
                          vips:    %w[] } ],

            'eth3' => [ { address: '***********',
                          dns:     '*******',
                          gateway: '**********',
                          mtu:     3333,
                          range:   '**********-**********54',
                          subnet:  '**********/24',
                          vips:    %w[] } ]
        })

        output = YAML.load(<<~'ONELEASE_CONF')
            ---
            eth1:
              server4:
                listen:
                - "%eth1"
                plugins:
                - lease_time: 3600s
                - server_id: ***********
                - dns: *******
                - mtu: 1111
                - router: *******
                - netmask: ***********
                - onelease: >-
                    leases-eth1.sqlite3 ********* ************* 3600s
                    --excluded-ips ***********,***********
                    --mac2ip --mac2ip-prefix 02:00
            eth2:
              server4:
                listen:
                - "%eth2"
                plugins:
                - lease_time: 3600s
                - server_id: ***********
                - dns: *******
                - mtu: 2222
                - router: **********
                - netmask: *********
                - onelease: >-
                    leases-eth2.sqlite3 *********** *********** 3600s
                    --excluded-ips ***********
                    --mac2ip --mac2ip-prefix 02:00
            eth3:
              server4:
                listen:
                - "%eth3"
                plugins:
                - lease_time: 3600s
                - server_id: ***********
                - dns: *******
                - mtu: 3333
                - router: **********
                - netmask: *************
                - onelease: >-
                    leases-eth3.sqlite3 ********** **********54 3600s
                    --excluded-ips ***********
                    --mac2ip --mac2ip-prefix 02:00
        ONELEASE_CONF

        allow(Service::DHCP4v2).to receive(:ip_link_show).and_return(
            { 'mtu' => 1111 },
            { 'mtu' => 2222 },
            { 'mtu' => 3333 }
        )

        Dir.mktmpdir do |dir|
            Service::DHCP4v2.configure basedir: dir
            result = YAML.load File.read("#{dir}/onelease-config.yml")
            expect(result).to eq output
        end
    end

    it 'should interpolate GW and DNS values' do
        clear_env

        ENV['ONEAPP_VNF_DHCP4_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_DHCP4_INTERFACES'] = 'eth0 eth1'

        ENV['ONEAPP_VNF_DHCP4_ETH0_GATEWAY'] = '<ETH0_EP0>'
        ENV['ONEAPP_VNF_DHCP4_ETH0_DNS'] = '<ETH0_EP0>'

        ENV['ONEAPP_VNF_DHCP4_ETH1_GATEWAY'] = '<ETH1_VIP0>'
        ENV['ONEAPP_VNF_DHCP4_ETH1_DNS'] = '<ETH1_VIP1>'

        ENV['ETH0_IP'] = '***********'
        ENV['ETH0_MASK'] = '*************'

        ENV['ETH1_IP'] = '***********'
        ENV['ETH1_MASK'] = '*************'

        ENV['ONEAPP_VROUTER_ETH0_VIP0'] = '***********'
        ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '***********'
        ENV['ONEAPP_VROUTER_ETH1_VIP1'] = '************'

        load './main.rb'; include Service::DHCP4v2

        allow(Service::DHCP4v2).to receive(:ip_link_show).and_return(
            { 'mtu' => 1111 },
            { 'mtu' => 2222 }
        )

        clear_vars Service::DHCP4v2

        expect(Service::DHCP4v2.parse_env).to eq ({
            'eth0' => [ { address: '***********',
                          dns:     '***********',
                          gateway: '***********',
                          mtu:     1111,
                          range:   '**********-**********54',
                          subnet:  '**********/24',
                          vips:    %w[***********] } ],

            'eth1' => [ { address: '***********',
                          dns:     '************',
                          gateway: '***********',
                          mtu:     2222,
                          range:   '**********-**********54',
                          subnet:  '**********/24',
                          vips:    %w[*********** ************] } ]
        })
    end
end
