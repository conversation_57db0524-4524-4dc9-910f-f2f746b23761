# coredhcp-onelease VRouter plugin for OpenNebula

This go module contains a wrapper for [coredhcp](https://github.com/coredhcp/coredhcp), that instantiates a coredhcp server for each interface indicated in the configuation file, allowing specifying configurations for requests coming from different interfaces. Those services could include our custom `onelease` plugin, which implements the OpenNebula IP Address lease based on the client's MAC address last four bytes (by default, the MAC address should start with the `02:00` prefix). We can also exclude some IPs from the lease adding them to the corresponding parameter.

# Execution

In order to run the server, you should execute the following commands:
```
go build .
sudo ./coredhcp-onelease
```

The coredhcp server will look for a configuration YAML file, for instance located in this same directory (config.yml). See the section below in order to see how to configure it.

# Configuration

In order to load the plugin on the server, you should have a `config.yml` configuration file in one of the following places:
```
* ./onelease-config.yml
* /coredhcp/onelease-config.yml
* /root/.coredhcp/onelease-config.yml
* /etc/coredhcp/onelease-config.yml
```

or you can pass the file explicitly when running the server with the `-c` option:

```
sudo ./coredhcp-onelease -c myconfig.yml
```


The config file content should contain the list of plugins and their arguments for each protocol version (DHCPv6 and DHCPv4), e.g.

```
eth0:
  server4:
    listen:
      - "%eth0"
    plugins:
      - lease_time: 3600s
      - server_id: *************
      - dns: ******* *******
      - router: *************
      - netmask: *************
      - onelease: leases-eth0.sqlite3 ************** ************** 3600s --excluded-ips **************,**************
        --mac2ip --mac2ip-prefix 04:00
eth1:
  server4:
    listen:
      - "%eth1"
    plugins:
      - lease_time: 3600s
      - server_id: ************
      - dns: ******* *******
      - router: ************
      - netmask: *************
      - onelease: leases-eth0.sqlite3 ************ ************00 3600s --excluded-ips *************,*************
        --mac2ip
```

The plugin parameters are
```
onelease: <lease_database_file> <lease_range_start_ip> <lease_range_end_ip> <lease_time> <optional_parameters>
```
Where optional parameters are:
* `--excluded-ips`: A comma-separated list of IPs from the range to be excluded from the allocation.
* `--mac2ip`: Enables MAC2IP address translation, i.e. it will allocate the specified IP from the least 4 bytes of the provided client MAC address (the IP should be in the lease range).
* `--mac2ip-prefix`: The MAC address 2-byte prefix for using in the MAC2IP feature (all the requests with this client MAC address 2-byte prefix will allocate the specified IP in the last 4 bytes). Defaults to "02:00".

[There](https://github.com/coredhcp/coredhcp/blob/master/cmds/coredhcp/config.yml.example) you have an example of each interface configuration in case you want to take it as reference, but as we are using a wrapper,
remember to nest the configuration on each interface tag.

# Testing

You can test the server features using the [client](./client/README.md) included in this module or any dhcp client tool like `dhclient` or `dhcping`.

# Maintenance

This module `main.go` file is a wrapper of the Coredhcp module, based on the source code generated by the [coredhcp-generator](https://github.com/coredhcp/coredhcp/tree/master/cmds/coredhcp-generator) and adapted for spawning multiple Coredhcp servers on each specified interface. In order to add or remove any plugin you should modify the `main.go` file from this module directly, as if substantially differs from the one generated by the `coredhcp-generator`.

# Licensing

The original work from the Coredhcp team and all the OpenNebula Systems modifications are licensed under the MIT License included in this directory.

