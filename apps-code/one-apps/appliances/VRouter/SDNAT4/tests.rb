# frozen_string_literal: true

require 'ipaddr'
require 'rspec'
require 'tmpdir'

def clear_vars(object)
    object.instance_variables.each { |name| object.remove_instance_variable(name) }
end

RSpec.describe self do
    it 'should extract sdnat4 info from vnets' do
        load './main.rb'; include Service::SDNAT4

        allow(Service::SDNAT4).to receive(:ip_addr_show).and_return({
            'ifname' => 'lo',
            'addr_info' => [
                { 'family'    => 'inet',
                  'local'     => '127.0.0.1',
                  'prefixlen' => 8,
                  'label'     => 'lo' },

                { 'family'    => 'inet',
                  'local'     => '***********',
                  'prefixlen' => 32,
                  'label'     => 'SDNAT4' },

               # { 'family'    => 'inet',
               #   'local'     => '***********',
               #   'prefixlen' => 32,
               #   'label'     => 'SDNAT4' }
            ]
        })

        (vnets ||= []) << JSON.parse(<<~'VNET0')
            {
              "VNET": {
                "ID": "0",
                "NAME": "service",
                "USED_LEASES": "6",
                "VROUTERS": {
                  "ID": [ "35" ]
                },
                "PARENT_NETWORK_ID": {},
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:c8",
                      "SIZE": "48",
                      "TYPE": "IP4",
                      "MAC_END": "02:00:0a:02:0b:f7",
                      "IP_END": "***********",
                      "USED_LEASES": "6",
                      "LEASES": {
                        "LEASE": [
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:c8", "VM": "265", "NIC_NAME": "NIC0" },
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:c9", "VM": "266", "NIC_NAME": "NIC0" },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:ca",
                            "VM": "267",
                            "PARENT": "NIC0",
                            "PARENT_NETWORK_ID": "1",
                            "EXTERNAL": true,
                            "NIC_NAME": "NIC0_ALIAS1"
                          },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:cb",
                            "VM": "268",
                            "PARENT": "NIC0",
                            "PARENT_NETWORK_ID": "1",
                            "EXTERNAL": true,
                            "NIC_NAME": "NIC0_ALIAS1"
                          },
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:cc", "VM": "269", "NIC_NAME": "NIC0" },
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:cd", "VM": "270", "NIC_NAME": "NIC0" }
                        ]
                      }
                    }
                  ]
                },
                "TEMPLATE": {
                  "NETWORK_ADDRESS": "*********",
                  "NETWORK_MASK": "*************",
                  "GATEWAY": "*********",
                  "DNS": "**********"
                }
              }
            }
        VNET0
        (vnets ||= []) << JSON.parse(<<~'VNET1')
            {
              "VNET": {
                "ID": "1",
                "NAME": "private",
                "USED_LEASES": "24",
                "VROUTERS": {
                  "ID": [ "35" ]
                },
                "PARENT_NETWORK_ID": {},
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:64",
                      "SIZE": "100",
                      "TYPE": "IP4",
                      "MAC_END": "02:00:ac:14:00:c7",
                      "IP_END": "************",
                      "USED_LEASES": "24",
                      "LEASES": {
                        "LEASE": [
                          { "IP": "************", "MAC": "02:00:ac:14:00:64", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:65", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:66", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:67", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:68", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:69", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6a", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6b", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6c", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6d", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6e", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6f", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:70", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:71", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:72", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:73", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:74", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:75", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:76", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:77", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:78", "VM": "267", "NIC_NAME": "NIC0" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:79", "VM": "268", "NIC_NAME": "NIC0" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:7a", "VM": "269", "NIC_NAME": "NIC1" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:7b", "VM": "270", "NIC_NAME": "NIC1" }
                        ]
                      }
                    }
                  ]
                },
                "TEMPLATE": {
                  "NETWORK_ADDRESS": "**********",
                  "NETWORK_MASK": "*************",
                  "GATEWAY": "***********"
                }
              }
            }
        VNET1

        clear_vars Service::SDNAT4

        Service::SDNAT4.instance_variable_set(:@subnets, [
            IPAddr.new('*********/24'),
            IPAddr.new('**********/16')
        ])

        expect(Service::SDNAT4.extract_external(vnets)).to eq ({
            external: [
                { 'EXTERNAL'          => true,
                  'IP'                => '***********',
                  'MAC'               => '02:00:0a:02:0b:ca',
                  'NIC_NAME'          => 'NIC0_ALIAS1',
                  'PARENT'            => 'NIC0',
                  'PARENT_NETWORK_ID' => '1',
                  'VM'                => '267' },

                { 'EXTERNAL'          => true,
                  'IP'                => '***********',
                  'MAC'               => '02:00:0a:02:0b:cb',
                  'NIC_NAME'          => 'NIC0_ALIAS1',
                  'PARENT'            => 'NIC0',
                  'PARENT_NETWORK_ID' => '1',
                  'VM'                => '268' }
            ],
            ip_map: { '***********' => '************',
                      '***********' => '************' },
            to_del: [],
            to_add: [ '***********' ]
        })
    end
end
