# frozen_string_literal: true

require 'json'
require 'rspec'
require_relative 'vrouter.rb'

def clear_env
    ENV.delete_if { |name| name.start_with?('ETH') || name.include?('VROUTER_') || name.include?('_VNF_') }
end

RSpec.describe 'infer_pfxlen' do
    it 'should find/guess cidr pfxlen' do
        tests = [
            [ nil, 0, '********', 8],
            [ nil, 0, '172.16.0.1', 16],
            [ nil, 0, '1**********', 24],

            ['***********', 1, '********', 15],
            ['***********', 1, '**********', 15],
            ['***********', 1, '***********', 15],

            ['***********', 2, '********/12', 12],
            ['***********', 2, '**********/12', 12],
            ['***********', 2, '***********/12', 12],

            [ nil, 3, '*******', 24],
            ['***********', 3, '*******', 16],
            ['***********', 3, '*******/12', 12]
        ]
        tests.each do |mask, eth_index, ip, pfxlen|
            clear_env
            ENV["ETH#{eth_index}_MASK"] = mask unless mask.nil?
            expect(infer_pfxlen(eth_index, ip)).to eq pfxlen
        end
    end
end

RSpec.describe 'detect_addrs' do
  it 'should parse IP variables with mask' do
      clear_env

      ENV['ETH0_IP']   = '*******'
      ENV['ETH0_MASK'] = '***********'
      ENV['ETH1_IP']   = '*******'
      ENV['ETH1_MASK'] = '*************'

      expect(detect_addrs)
          .to eq({
                     'eth0' => { 'ETH0_IP0' => '*******/16' },
                     'eth1' => { 'ETH1_IP0' => '*******/24' }
                 })
  end

  it 'should parse IP variables with network address' do
      clear_env

      ENV['ETH0_IP'] = '********'
      ENV['ETH0_NETWORK'] = '10.0.0.0'
      ENV['ETH1_IP'] = '**********'
      ENV['ETH1_NETWORK'] = '**********'

      expect(detect_addrs)
          .to eq({
                     'eth0' => { 'ETH0_IP0' => '********/8' },
                     'eth1' => { 'ETH1_IP0' => '**********/16' }
                 })
  end

  it 'should handle private network ranges without mask or network' do
      clear_env

      ENV['ETH0_IP'] = '********'
      ENV['ETH1_IP'] = '**********'
      ENV['ETH2_IP'] = '***********'

      expect(detect_addrs)
          .to eq({
                     'eth0' => { 'ETH0_IP0' => '********/8' },
                     'eth1' => { 'ETH1_IP0' => '**********/16' },
                     'eth2' => { 'ETH2_IP0' => '***********/24' }
                 })
  end
end

RSpec.describe 'detect_vips' do
  it 'should parse VIP variables with mask' do
      clear_env

      ENV['ETH0_MASK']                = '***********'
      ENV['ETH0_VROUTER_IP']          = '*******'
      ENV['ONEAPP_VROUTER_ETH0_VIP1'] = '*******/24'
      ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '*******'

      expect(detect_vips)
          .to eq({
                     'eth0' => { 'ETH0_VIP0' => '*******/16',
                                 'ETH0_VIP1' => '*******/24' },
                     'eth1' => { 'ETH1_VIP0' => '*******/24' }
                 })
  end

  it 'should parse VIP variables with network address' do
      clear_env

      ENV['ETH0_NETWORK'] = '10.0.0.0'
      ENV['ETH0_VROUTER_IP'] = '********'
      ENV['ONEAPP_VROUTER_ETH0_VIP1'] = '********'
      ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '**********'
      ENV['ETH1_NETWORK'] = '**********'

      expect(detect_vips)
          .to eq({
                     'eth0' => { 'ETH0_VIP0' => '********/8',
                                 'ETH0_VIP1' => '********/8' },
                     'eth1' => { 'ETH1_VIP0' => '**********/16' }
                 })
  end

  it 'should default to /24 for public VIPs without network or mask' do
      clear_env

      ENV['ETH0_VROUTER_IP'] = '*******'
      ENV['ONEAPP_VROUTER_ETH0_VIP1'] = '*******'

      expect(detect_vips)
          .to eq({
                     'eth0' => { 'ETH0_VIP0' => '*******/24',
                                 'ETH0_VIP1' => '*******/24' }
                 })
  end

  it 'should infer prefix for private range VIPs without mask or network' do
      clear_env

      ENV['ONEAPP_VROUTER_ETH0_VIP0'] = '**********'
      ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '********'
      ENV['ONEAPP_VROUTER_ETH2_VIP0'] = '***********'

      expect(detect_vips)
          .to eq({
                     'eth0' => { 'ETH0_VIP0' => '**********/16' },
                     'eth1' => { 'ETH1_VIP0' => '********/8' },
                     'eth2' => { 'ETH2_VIP0' => '***********/24' }
                 })
  end
end

RSpec.describe 'detect_endpoints' do
  it 'should merge IP and VIP variables correctly (with mask)' do
      clear_env

      ENV['ETH0_IP']   = '*******'
      ENV['ETH0_MASK'] = '***********'
      ENV['ETH1_IP']   = '*******'
      ENV['ETH1_MASK'] = '*************'

      ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '*******'

      expect(detect_endpoints)
          .to eq({
                     'eth0' => { 'ETH0_EP0' => '*******/16' },
                     'eth1' => { 'ETH1_EP0' => '*******/24' }
                 })
  end

  it 'should use network address when mask is not available' do
      clear_env

      ENV['ETH0_IP'] = '*******'
      ENV['ETH0_NETWORK'] = '*******'
      ENV['ETH1_IP'] = '*******'
      ENV['ETH1_NETWORK'] = '*******'
      ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '*******'

      expect(detect_endpoints)
          .to eq({
                     'eth0' => { 'ETH0_EP0' => '*******/16' },
                     'eth1' => { 'ETH1_EP0' => '*******/16' }
                 })
  end

  it 'should handle private network ranges without mask or network' do
      clear_env

      ENV['ETH0_IP'] = '********'
      ENV['ETH1_IP'] = '**********'
      ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '**********'

      expect(detect_endpoints)
          .to eq({
                     'eth0' => { 'ETH0_EP0' => '********/8' },
                     'eth1' => { 'ETH1_EP0' => '**********/16' }
                 })
  end
end

RSpec.describe 'parse_interfaces' do
    it 'should return empty interfaces with nil input' do
        expect(parse_interfaces(nil)).to be_empty
    end

    it 'should parse interfaces from a string' do
        allow(self).to receive(:detect_nics).and_return([
            'eth0',
            'eth1',
            'eth2',
            'eth3'
        ])
        allow(self).to receive(:addrs_to_nics).and_return({
            '********' => ['eth0', 'eth2'],
            '********' => ['eth1'],
            '********' => ['eth1']
        })
        tests = [
            [ '********', { 'eth0' => [ { name: 'eth0', addr: '********', port: nil } ],
                            'eth2' => [ { name: 'eth2', addr: '********', port: nil } ] } ],

            [ '********@53', { 'eth0' => [ { name: 'eth0', addr: '********', port: '53' } ],
                               'eth2' => [ { name: 'eth2', addr: '********', port: '53' } ] } ],

            [ 'eth0/********', { 'eth0' => [ { name: 'eth0', addr: '********', port: nil } ] } ],

            [ 'eth0/********@53', { 'eth0' => [ { name: 'eth0', addr: '********', port: '53' } ] } ],

            [ '********@53 ********@53', { 'eth1' => [ { name: 'eth1', addr: '********', port: '53' },
                                                       { name: 'eth1', addr: '********', port: '53' } ] } ],

            [ 'eth7/10.0.0.7 ********@53', { 'eth1' => [ { name: 'eth1', addr: '********', port: '53' } ],
                                             'eth7' => [ { name: 'eth7', addr: '10.0.0.7', port: nil  } ] } ],

            [ 'eth0/********@53 eth1/********@53', { 'eth0' => [ { name: 'eth0', addr: '********', port: '53' } ],
                                                     'eth1' => [ { name: 'eth1', addr: '********', port: '53' } ] } ],

            [ 'eth0/******** eth1/********', { 'eth0' => [ { name: 'eth0', addr: '********', port: nil } ],
                                               'eth1' => [ { name: 'eth1', addr: '********', port: nil } ] } ],

            [ 'eth0 eth1,eth2;eth3', { 'eth0' => [ { name: 'eth0', addr: nil, port: nil } ],
                                       'eth1' => [ { name: 'eth1', addr: nil, port: nil } ],
                                       'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                                       'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] } ],

            [ 'eth0/********@', { 'eth0' => [ { name: 'eth0', addr: '********', port: nil } ] } ],

            [ 'eth0/********', { 'eth0' => [ { name: 'eth0', addr: '********', port: nil } ] } ],

            [ 'eth0/', { 'eth0' => [ { name: 'eth0', addr: nil, port: nil } ] } ],

            [ 'eth0', { 'eth0' => [ { name: 'eth0', addr: nil, port: nil } ] } ],

            [ '', { 'eth0' => [ { name: 'eth0', addr: nil, port: nil } ],
                    'eth1' => [ { name: 'eth1', addr: nil, port: nil } ],
                    'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                    'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] } ]
        ]
        tests.each do |input, output|
            expect(parse_interfaces(input)).to eq output
        end
    end

    it 'should parse interfaces from a string (negation)' do
        allow(self).to receive(:detect_nics).and_return([
            'eth0',
            'eth1',
            'eth2',
            'eth3'
        ])
        allow(self).to receive(:addrs_to_nics).and_return({
            '********' => ['eth0', 'eth2'],
            '********' => ['eth1']
        })
        tests = [
            [ 'eth0/********@53 eth1 eth2 !********', { 'eth0' => [ { name: 'eth0', addr: '********', port: '53' } ],
                                                        'eth2' => [ { name: 'eth2', addr:        nil, port:  nil } ] } ],

            [ '!eth1 ********@53 eth3', { 'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] } ],

            [ '********@53 eth3', { 'eth1' => [ { name: 'eth1', addr: '********', port: '53' } ],
                                    'eth3' => [ { name: 'eth3', addr:        nil, port:  nil } ] } ],

            [ '!********', { 'eth0' => [ { name: 'eth0', addr: nil, port: nil } ],
                             'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                             'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] } ],

            [ '!eth0 !eth2', { 'eth1' => [ { name: 'eth1', addr: nil, port: nil } ],
                               'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] } ],

            [ '!eth0', { 'eth1' => [ { name: 'eth1', addr: nil, port: nil } ],
                         'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                         'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] } ]
        ]
        tests.each do |input, output|
            expect(parse_interfaces(input)).to eq output
        end
    end
end

RSpec.describe 'render_interface' do
    it 'should render interfaces from parts' do
        tests = [
            [ { name: 'eth0', addr: nil  , port: nil   },
              { name: true  , addr: false, port: false }, 'eth0' ],

            [ { name: 'eth0', addr: nil  , port: nil   },
              { name: false , addr: false, port: false }, 'eth0' ],

            [ { name: 'eth0', addr: '********', port: nil   },
              { name: true  , addr: false     , port: false }, 'eth0' ],

            [ { name: 'eth0', addr: '********', port: nil   },
              { name: true  , addr: true      , port: false }, 'eth0/********' ],

            [ { name: 'eth0', addr: '********', port: nil   },
              { name: false , addr: true      , port: false }, '********' ],

            [ { name: 'eth0', addr: '********', port: '53' },
              { name:  true , addr: true      , port: true }, 'eth0/********@53' ],

            [ { name: 'eth0', addr: '********', port: '53'  },
              { name:  true , addr: true      , port: false }, 'eth0/********' ],

            [ { name: 'eth0', addr: '********', port: '53' },
              { name:  true , addr: false     , port: true }, 'eth0@53' ],

            [ { name: 'eth0', addr: '********', port: '53' },
              { name: false , addr: true      , port: true }, '********@53' ]
        ]
        tests.each do |input, options, output|
            expect(render_interface(input, **options)).to eq output
        end
    end
end

RSpec.describe 'nics_to_addrs' do
    it 'should map nics to addrs' do
        clear_env

        ENV['ETH0_IP'] = '********'
        ENV['ETH1_IP'] = '**********'
        ENV['ETH2_IP'] = '**********'
        ENV['ETH3_IP'] = '**********'

        tests = [
            [ %w[eth0], { 'eth0' => %w[********] } ],

            [ %w[eth1 eth2 eth3], { 'eth1' => %w[**********],
                                    'eth2' => %w[**********],
                                    'eth3' => %w[**********] } ]
        ]
        tests.each do |input, output|
            expect(nics_to_addrs(input)).to eq output
        end
    end
end

RSpec.describe 'addrs_to_nics' do
    it 'should map addrs to nics' do
        clear_env

        ENV['ETH0_IP'] = '********'
        ENV['ETH1_IP'] = '**********'
        ENV['ETH2_IP'] = '**********'
        ENV['ETH3_IP'] = '**********'

        tests = [
            [ %w[eth0], { '********' => %w[eth0] } ],

            [ %w[eth1 eth2 eth3], { '**********' => %w[eth1],
                                    '**********' => %w[eth2 eth3] } ]
        ]
        tests.each do |input, output|
            expect(addrs_to_nics(input)).to eq output
        end
    end

    it 'should map addrs to nics (:noip)' do
        clear_env

        ENV['ETH0_IP'] = ''
        ENV['ETH1_IP'] = '**********'
        ENV['ETH2_IP'] = ''
        ENV['ETH3_IP'] = '**********'

        tests = [
            [ %w[eth0 eth1], { '**********' => %w[eth1] } ],

            [ %w[eth0 eth1 eth2 eth3], { '**********' => %w[eth1],
                                         '**********' => %w[eth3] } ]
        ]
        tests.each do |input, output|
            expect(addrs_to_nics(input)).to eq output
        end
    end
end

RSpec.describe 'addrs_to_subnets' do
    it 'should extract subnets' do
        clear_env

        ENV['ETH0_IP'] = '********'
        ENV['ETH0_MASK'] = '***************'
        ENV['ETH1_IP'] = '**********'
        ENV['ETH1_MASK'] = '***********'
        ENV['ETH2_IP'] = '**********'
        ENV['ETH2_MASK'] = '*************'

        tests = [
            [ %w[eth0], { '********/32' => '********/32' } ],

            [ %w[eth1 eth2], { '**********/16' => '**********/16',
                               '**********/24' => '**********/24' } ]
        ]
        tests.each do |input, output|
            expect(addrs_to_subnets(input)).to eq output
        end
    end
end

RSpec.describe 'vips_to_subnets' do
    it 'should extract subnets' do
        clear_env

        ENV['ETH0_MASK'] = '*************'

        tests = [
            [ [ 'eth0', 'eth1' ],

              { 'eth0' => { 'ONEAPP_VROUTER_ETH0_VIP0'=> '*******',
                            'ONEAPP_VROUTER_ETH0_VIP1'=> '*******/16' },
                'eth1' => { 'ONEAPP_VROUTER_ETH1_VIP0'=> '*******' } },

              { '*******/24' => '*******/24',
                '*******/16' => '*******/16',
                '*******/24' => '*******/24' } ]
        ]
        tests.each do |nics, vips, output|
            expect(vips_to_subnets(nics, vips)).to eq output
        end
    end
end

RSpec.describe 'subnets_to_ranges' do
    it 'should convert subnets to ranges' do
        tests = [
            [ [ '**********/16', '**********/24' ],
              { '**********/16' => '**********-**************',
                '**********/24' => '**********-************' } ],

            [ [ '2001:db8:1:0::/64', '2001:db8:1:1::/64' ],
              { '2001:db8:1:0::/64' => '2001:db8:1::2-2001:db8:1:0:ffff:ffff:ffff:fffe',
                '2001:db8:1:1::/64' => '2001:db8:1:1::2-2001:db8:1:1:ffff:ffff:ffff:fffe' } ]
        ]
        tests.each do |input, output|
            expect(subnets_to_ranges(input)).to eq output
        end
    end
end

RSpec.describe 'get_service_vms' do
    it 'should list all available vms (oneflow)' do
        ENV['ONEGATE_ENDPOINT'] = 'http://127.0.0.1:5030'

        allow(OneGate.instance).to receive(:service_show).and_return(JSON.parse(<<~'SERVICE_SHOW'))
            {
              "SERVICE": {
                "name": "asd",
                "id": "23",
                "state": 1,
                "roles": [
                  {
                    "name": "server",
                    "cardinality": 2,
                    "state": 1,
                    "nodes": [
                      {
                        "deploy_id": 435,
                        "running": null,
                        "vm_info": {
                          "VM": {
                            "ID": "435",
                            "UID": "0",
                            "GID": "0",
                            "UNAME": "oneadmin",
                            "GNAME": "oneadmin",
                            "NAME": "server_0_(service_23)"
                          }
                        }
                      },
                      {
                        "deploy_id": 436,
                        "running": null,
                        "vm_info": {
                          "VM": {
                            "ID": "436",
                            "UID": "0",
                            "GID": "0",
                            "UNAME": "oneadmin",
                            "GNAME": "oneadmin",
                            "NAME": "server_1_(service_23)"
                          }
                        }
                      }
                    ]
                  }
                ]
              }
            }
        SERVICE_SHOW
        (vms ||= []) << JSON.parse(<<~'VM0_SHOW')
            {
              "VM": {
                "NAME": "server_0_(service_23)",
                "ID": "435",
                "STATE": "3",
                "LCM_STATE": "3",
                "USER_TEMPLATE": {
                  "HOT_RESIZE": {
                    "CPU_HOT_ADD_ENABLED": "NO",
                    "MEMORY_HOT_ADD_ENABLED": "NO"
                  },
                  "LOGO": "images/logos/linux.png",
                  "LXD_SECURITY_PRIVILEGED": "true",
                  "MEMORY_UNIT_COST": "MB",
                  "ONEGATE_HAPROXY_LB0_IP": "**********",
                  "ONEGATE_HAPROXY_LB0_PORT": "5432",
                  "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                  "ONEGATE_HAPROXY_LB0_SERVER_PORT": "2345",
                  "ROLE_NAME": "server",
                  "SERVICE_ID": "23"
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:ca",
                      "NAME": "_NIC0",
                      "NETWORK": "service"
                    },
                    {
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:7a",
                      "NAME": "_NIC1",
                      "NETWORK": "private"
                    }
                  ],
                  "NIC_ALIAS": []
                }
              }
            }
        VM0_SHOW
        (vms ||= []) << JSON.parse(<<~'VM1_SHOW')
            {
              "VM": {
                "NAME": "server_1_(service_23)",
                "ID": "436",
                "STATE": "3",
                "LCM_STATE": "3",
                "USER_TEMPLATE": {
                  "HOT_RESIZE": {
                    "CPU_HOT_ADD_ENABLED": "NO",
                    "MEMORY_HOT_ADD_ENABLED": "NO"
                  },
                  "LOGO": "images/logos/linux.png",
                  "LXD_SECURITY_PRIVILEGED": "true",
                  "MEMORY_UNIT_COST": "MB",
                  "ONEGATE_HAPROXY_LB0_IP": "**********",
                  "ONEGATE_HAPROXY_LB0_PORT": "5432",
                  "ONEGATE_HAPROXY_LB0_SERVER_HOST": "***********",
                  "ONEGATE_HAPROXY_LB0_SERVER_PORT": "2345",
                  "ROLE_NAME": "server",
                  "SERVICE_ID": "23"
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:cb",
                      "NAME": "_NIC0",
                      "NETWORK": "service"
                    },
                    {
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:7b",
                      "NAME": "_NIC1",
                      "NETWORK": "private"
                    }
                  ],
                  "NIC_ALIAS": []
                }
              }
            }
        VM1_SHOW

        allow(OneGate.instance).to receive(:vm_show).and_return(*vms)

        expect(get_service_vms).to eq vms
    end
end

RSpec.describe 'get_vrouter_vnets' do
    it 'should recursively resolve all viable vnets' do
        ENV['ONEGATE_ENDPOINT'] = 'http://127.0.0.1:5030'

        allow(OneGate.instance).to receive(:vrouter_show).and_return(JSON.parse(<<~'VROUTER_SHOW'))
            {
              "VROUTER": {
                "NAME": "vrouter",
                "ID": "12",
                "VMS": {
                  "ID": [ "115" ]
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "NETWORK": "service",
                      "NETWORK_ID": "0",
                      "NIC_ID": "0"
                    },
                    {
                      "NETWORK": "private",
                      "NETWORK_ID": "1",
                      "NIC_ID": "1"
                    }
                  ],
                  "TEMPLATE_ID": "74"
                }
              }
            }
        VROUTER_SHOW

        (vnets ||= []) << JSON.parse(<<~'SERVICE_VNET_SHOW')
            {
              "VNET": {
                "ID": "0",
                "NAME": "service",
                "USED_LEASES": "4",
                "VROUTERS": {
                  "ID": [ "12" ]
                },
                "PARENT_NETWORK_ID": {},
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:c8",
                      "SIZE": "48",
                      "TYPE": "IP4",
                      "MAC_END": "02:00:0a:02:0b:f7",
                      "IP_END": "***********",
                      "USED_LEASES": "4",
                      "LEASES": {
                        "LEASE": [
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:c8", "VM": "110", "NIC_NAME": "NIC0" },
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:c9", "VM": "111", "NIC_NAME": "NIC0" },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:ca",
                            "VM": "113",
                            "PARENT": "NIC0",
                            "PARENT_NETWORK_ID": "40",
                            "EXTERNAL": true,
                            "NIC_NAME": "NIC0_ALIAS1"
                          },
                          { "IP": "***********", "MAC": "02:00:0a:02:0b:cc", "VM": "115", "NIC_NAME": "NIC0" }
                        ]
                      }
                    }
                  ]
                },
                "TEMPLATE": {
                  "NETWORK_ADDRESS": "*********",
                  "NETWORK_MASK": "*************",
                  "GATEWAY": "*********",
                  "DNS": "**********"
                }
              }
            }
        SERVICE_VNET_SHOW
        (vnets ||= []) << JSON.parse(<<~'PRIVATE_VNET_SHOW')
            {
              "VNET": {
                "ID": "1",
                "NAME": "private",
                "USED_LEASES": "21",
                "VROUTERS": {
                  "ID": [ "12" ]
                },
                "PARENT_NETWORK_ID": {},
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:64",
                      "SIZE": "100",
                      "TYPE": "IP4",
                      "MAC_END": "02:00:ac:14:00:c7",
                      "IP_END": "************",
                      "USED_LEASES": "21",
                      "LEASES": {
                        "LEASE": [
                          { "IP": "************", "MAC": "02:00:ac:14:00:64", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:65", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:66", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:67", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:68", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:69", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6a", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6b", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6c", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6d", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6e", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:6f", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:70", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:71", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:72", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:73", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:74", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:75", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:76", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:77", "VNET": "40" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:79", "VM": "115", "NIC_NAME": "NIC1" }
                        ]
                      }
                    }
                  ]
                },
                "TEMPLATE": {
                  "NETWORK_ADDRESS": "**********",
                  "NETWORK_MASK": "*************",
                  "GATEWAY": "***********"
                }
              }
            }
        PRIVATE_VNET_SHOW
        (vnets ||= []) << JSON.parse(<<~'RESERVATION_VNET_SHOW')
            {
              "VNET": {
                "ID": "40",
                "NAME": "reservation",
                "USED_LEASES": "2",
                "VROUTERS": {
                  "ID": []
                },
                "PARENT_NETWORK_ID": "1",
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:64",
                      "PARENT_NETWORK_AR_ID": "0",
                      "SIZE": "20",
                      "TYPE": "IP4",
                      "MAC_END": "02:00:ac:14:00:77",
                      "IP_END": "************",
                      "USED_LEASES": "2",
                      "LEASES": {
                        "LEASE": [
                          { "IP": "************", "MAC": "02:00:ac:14:00:64", "VM": "112", "NIC_NAME": "NIC0" },
                          { "IP": "************", "MAC": "02:00:ac:14:00:65", "VM": "113", "NIC_NAME": "NIC0" }
                        ]
                      }
                    }
                  ]
                },
                "TEMPLATE": {
                  "NETWORK_ADDRESS": "**********",
                  "NETWORK_MASK": "*************",
                  "GATEWAY": "***********"
                }
              }
            }
        RESERVATION_VNET_SHOW

        allow(OneGate.instance).to receive(:vnet_show).and_return(*vnets)

        expect(get_vrouter_vnets).to eq vnets
    end
end

RSpec.describe 'backends.from_env' do
    it 'should correctly extract backends from env vars' do
        clear_env

        ENV['ONEAPP_VNF_LB0_IP'] = '**********'
        ENV['ONEAPP_VNF_LB0_PORT'] = '6969'
        ENV['ONEAPP_VNF_LB0_PROTOCOL'] = 'TCP'

        ENV['ONEAPP_VNF_LB0_SERVER0_HOST'] = 'asd0'
        ENV['ONEAPP_VNF_LB0_SERVER0_PORT'] = '1234'
        ENV['ONEAPP_VNF_LB0_SERVER0_WEIGHT'] = '1'

        ENV['ONEAPP_VNF_LB0_SERVER1_HOST'] = 'asd1'
        ENV['ONEAPP_VNF_LB0_SERVER1_PORT'] = '1234'
        ENV['ONEAPP_VNF_LB0_SERVER1_WEIGHT'] = '2'

        ENV['ONEAPP_VNF_LB0_SERVER2_HOST'] = 'asd2'
        ENV['ONEAPP_VNF_LB0_SERVER2_PORT'] = '1234'
        ENV['ONEAPP_VNF_LB0_SERVER2_WEIGHT'] = '3'

        ENV['ONEAPP_VNF_LB1_IP'] = '**********'
        ENV['ONEAPP_VNF_LB1_PORT'] = '8686'
        ENV['ONEAPP_VNF_LB1_PROTOCOL'] = 'TCP'

        ENV['ONEAPP_VNF_LB1_SERVER0_HOST'] = 'asd0'
        ENV['ONEAPP_VNF_LB1_SERVER0_PORT'] = '4321'
        ENV['ONEAPP_VNF_LB1_SERVER0_WEIGHT'] = '1'

        ENV['ONEAPP_VNF_LB1_SERVER1_HOST'] = 'asd1'
        ENV['ONEAPP_VNF_LB1_SERVER1_PORT'] = '4321'
        ENV['ONEAPP_VNF_LB1_SERVER1_WEIGHT'] = '2'

        expect(backends.from_env).to eq ({
            by_endpoint: {
                [ 0, '**********', '6969' ] =>
                    { [ 'asd0', '1234' ] => { host: 'asd0', port: '1234', weight: '1' },
                      [ 'asd1', '1234' ] => { host: 'asd1', port: '1234', weight: '2' },
                      [ 'asd2', '1234' ] => { host: 'asd2', port: '1234', weight: '3' } },

                [ 1, '**********', '8686' ] =>
                    { [ 'asd0', '4321' ] => { host: 'asd0', port: '4321', weight: '1' },
                      [ 'asd1', '4321' ] => { host: 'asd1', port: '4321', weight: '2' } } },

            options: { 0 => { ip: '**********', port: '6969', protocol: 'TCP' },
                       1 => { ip: '**********', port: '8686', protocol: 'TCP' } }
        })
    end

    it 'should accept nil ports when requested' do
        clear_env

        ENV['ONEAPP_VNF_LB0_IP'] = '**********'
        ENV['ONEAPP_VNF_LB0_PROTOCOL'] = 'TCP'

        ENV['ONEAPP_VNF_LB0_SERVER0_HOST'] = 'asd0'
        ENV['ONEAPP_VNF_LB0_SERVER0_WEIGHT'] = '1'

        ENV['ONEAPP_VNF_LB0_SERVER1_HOST'] = 'asd1'
        ENV['ONEAPP_VNF_LB0_SERVER1_WEIGHT'] = '2'

        ENV['ONEAPP_VNF_LB0_SERVER2_HOST'] = 'asd2'
        ENV['ONEAPP_VNF_LB0_SERVER2_WEIGHT'] = '3'

        expect(backends.from_env(allow_nil_ports: true)).to eq ({
            by_endpoint: {
                [ 0, '**********', nil ] =>
                    { [ 'asd0', nil ] => { host: 'asd0', weight: '1' },
                      [ 'asd1', nil ] => { host: 'asd1', weight: '2' },
                      [ 'asd2', nil ] => { host: 'asd2', weight: '3' } } },

            options: { 0 => { ip: '**********', protocol: 'TCP' } }
        })
    end
end

RSpec.describe 'backends.from_vnets' do
    it 'should correctly extract backends from vnets' do
        (vnets ||= []) << JSON.parse(<<~'VNET0')
            {
              "VNET": {
                "ID": "0",
                "AR_POOL": {
                  "AR": [
                    {
                      "AR_ID": "0",
                      "LEASES": {
                        "LEASE": [
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:ca",
                            "VM": "167",
                            "NIC_NAME": "NIC0",
                            "BACKEND": "YES",

                            "ONEGATE_LB0_IP": "**********",
                            "ONEGATE_LB0_PORT": "6969",
                            "ONEGATE_LB0_SERVER_HOST": "asd2",
                            "ONEGATE_LB0_SERVER_PORT": "1234",
                            "ONEGATE_LB0_SERVER_WEIGHT": "3"
                          },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:c9",
                            "VM": "167",
                            "NIC_NAME": "NIC0",
                            "BACKEND": "YES",

                            "ONEGATE_LB0_IP": "**********",
                            "ONEGATE_LB0_PORT": "6969",
                            "ONEGATE_LB0_SERVER_HOST": "asd1",
                            "ONEGATE_LB0_SERVER_PORT": "1234",
                            "ONEGATE_LB0_SERVER_WEIGHT": "2",

                            "ONEGATE_LB1_IP": "**********",
                            "ONEGATE_LB1_PORT": "8686",
                            "ONEGATE_LB1_SERVER_HOST": "asd1",
                            "ONEGATE_LB1_SERVER_PORT": "4321",
                            "ONEGATE_LB1_SERVER_WEIGHT": "2"
                          },
                          {
                            "IP": "***********",
                            "MAC": "02:00:0a:02:0b:c8",
                            "VM": "167",
                            "NIC_NAME": "NIC0",
                            "BACKEND": "YES",

                            "ONEGATE_LB0_IP": "**********",
                            "ONEGATE_LB0_PORT": "6969",
                            "ONEGATE_LB0_SERVER_HOST": "asd0",
                            "ONEGATE_LB0_SERVER_PORT": "1234",
                            "ONEGATE_LB0_SERVER_WEIGHT": "1",

                            "ONEGATE_LB1_IP": "**********",
                            "ONEGATE_LB1_PORT": "8686",
                            "ONEGATE_LB1_SERVER_HOST": "asd0",
                            "ONEGATE_LB1_SERVER_PORT": "4321",
                            "ONEGATE_LB1_SERVER_WEIGHT": "1"
                          }
                        ]
                      }
                    }
                  ]
                }
              }
            }
        VNET0
        expect(backends.from_vnets(vnets)).to eq ({
            by_endpoint: {
                [ 0, '**********', '6969' ] =>
                    { [ 'asd0', '1234' ] => { host: 'asd0', port: '1234', weight: '1' },
                      [ 'asd1', '1234' ] => { host: 'asd1', port: '1234', weight: '2' },
                      [ 'asd2', '1234' ] => { host: 'asd2', port: '1234', weight: '3' } },

                [ 1, '**********', '8686' ] =>
                    { [ 'asd0', '4321' ] => { host: 'asd0', port: '4321', weight: '1' },
                      [ 'asd1', '4321' ] => { host: 'asd1', port: '4321', weight: '2' } } },

            options: { 0 => { ip: '**********', port: '6969' },
                       1 => { ip: '**********', port: '8686' } }
        })
    end
end

RSpec.describe 'backends.from_vms' do
    it 'should correctly extract backends from vms (oneflow)' do
        (vms ||= []) << JSON.parse(<<~'VM0')
            {
              "VM": {
                "NAME": "server_0_(service_23)",
                "ID": "435",
                "STATE": "3",
                "LCM_STATE": "3",
                "USER_TEMPLATE": {
                  "HOT_RESIZE": {
                    "CPU_HOT_ADD_ENABLED": "NO",
                    "MEMORY_HOT_ADD_ENABLED": "NO"
                  },
                  "LOGO": "images/logos/linux.png",
                  "LXD_SECURITY_PRIVILEGED": "true",
                  "MEMORY_UNIT_COST": "MB",
                  "ONEGATE_LB0_IP": "**********",
                  "ONEGATE_LB0_PORT": "5432",
                  "ONEGATE_LB0_SERVER_HOST": "***********",
                  "ONEGATE_LB0_SERVER_PORT": "2345",
                  "ROLE_NAME": "server",
                  "SERVICE_ID": "23"
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:ca",
                      "NAME": "_NIC0",
                      "NETWORK": "service"
                    },
                    {
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:7a",
                      "NAME": "_NIC1",
                      "NETWORK": "private"
                    }
                  ],
                  "NIC_ALIAS": []
                }
              }
            }
        VM0
        (vms ||= []) << JSON.parse(<<~'VM1')
            {
              "VM": {
                "NAME": "server_1_(service_23)",
                "ID": "436",
                "STATE": "3",
                "LCM_STATE": "3",
                "USER_TEMPLATE": {
                  "HOT_RESIZE": {
                    "CPU_HOT_ADD_ENABLED": "NO",
                    "MEMORY_HOT_ADD_ENABLED": "NO"
                  },
                  "LOGO": "images/logos/linux.png",
                  "LXD_SECURITY_PRIVILEGED": "true",
                  "MEMORY_UNIT_COST": "MB",
                  "ONEGATE_LB0_IP": "**********",
                  "ONEGATE_LB0_PORT": "5432",
                  "ONEGATE_LB0_SERVER_HOST": "***********",
                  "ONEGATE_LB0_SERVER_PORT": "2345",
                  "ROLE_NAME": "server",
                  "SERVICE_ID": "23"
                },
                "TEMPLATE": {
                  "NIC": [
                    {
                      "IP": "***********",
                      "MAC": "02:00:0a:02:0b:cb",
                      "NAME": "_NIC0",
                      "NETWORK": "service"
                    },
                    {
                      "IP": "************",
                      "MAC": "02:00:ac:14:00:7b",
                      "NAME": "_NIC1",
                      "NETWORK": "private"
                    }
                  ],
                  "NIC_ALIAS": []
                }
              }
            }
        VM1

        expect(backends.from_vms(vms)).to eq ({
            by_endpoint: {
                [ 0, '**********', '5432'] =>
                    { [ '***********', '2345' ] => { host: '***********', port: '2345' },
                      [ '***********', '2345' ] => { host: '***********', port: '2345' } } },

            options: { 0 => { ip: '**********', port: '5432' } }
        })
    end
end

RSpec.describe 'backends.combine' do
    it 'should filter + merge dynamic endpoints' do
        tests = [
            [ # Add dynamic backend.
                { by_endpoint: {},
                  options:     { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } }
            ],
            [ # No change (LB0 IP / PORT mismatch).
                { by_endpoint: {},
                  options:     { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '1111'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '1111' } } },

                { by_endpoint: {},
                  options:     { 0 => { ip: '**********', port: '5432' } } }
            ],
            [ # No change (dynamic is a subset of static).
                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } },
                    [ 1, '**********', '1111'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' },
                             1 => { ip: '**********', port: '1111' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } },
                    [ 1, '**********', '1111'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' },
                             1 => { ip: '**********', port: '1111' } } },
            ],
            [ # No change (LB1 is undefined).
                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } },
                    [ 1, '**********', '1111'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' },
                             1 => { ip: '**********', port: '1111' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } }
            ],
            [ # Add second backend.
                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '1111' ] => { host: '***********', port: '1111' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } },

                { by_endpoint: {
                    [ 0, '**********', '5432'] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' },
                          [ '***********', '1111' ] => { host: '***********', port: '1111' } } },
                  options: { 0 => { ip: '**********', port: '5432' } } }
            ]
        ]
        tests.each do |static, dynamic, output|
            expect(backends.combine(static, dynamic)).to eq output
        end
    end
end

RSpec.describe 'backends.resolve' do
    it 'should replace (v)ip placeholders with existing (v)ip addresses' do
        tests = [
            [
                # "addrs"
                { 'eth0' => { 'ETH0_IP0' => '*******/24' },
                  'eth1' => { 'ETH1_IP0' => '*******/24' } },

                # "vips"
                { 'eth0' => { 'ETH0_VIP0' => '*********/24',
                              'ETH0_VIP1' => '*********/24' } },

                # "endpoints"
                { 'eth0' => { 'ETH0_EP0' => '*********/24',
                              'ETH0_EP1' => '*********/24' },
                  'eth1' => { 'ETH1_EP0' => '*******/24' } },

                { by_endpoint: {
                    [ 0, '<ETH0_VIP0>', '5432' ] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } },

                    [ 1, '<ETH1_EP0>', '1111' ] =>
                        { [ '***********', '2222' ] => { host: '***********', port: '2222' } },

                    [ 2, '*******', '3333' ] =>
                        { [ '***********', '4444' ] => { host: '***********', port: '4444' } } },

                  options: { 0 => { ip: '<ETH0_VIP0>', port: '5432' },
                             1 => { ip: '<ETH1_EP0>', port: '1111' },
                             2 => { ip: '*******', port: '3333' } } },

                { by_endpoint: {
                    [ 0, '*********', '5432' ] =>
                        { [ '***********', '2345' ] => { host: '***********', port: '2345' } },

                    [ 1, '*******', '1111' ] =>
                        { [ '***********', '2222' ] => { host: '***********', port: '2222' } },

                    [ 2, '*******', '3333' ] =>
                        { [ '***********', '4444' ] => { host: '***********', port: '4444' } } },

                  options: { 0 => { ip: '*********', port: '5432' },
                             1 => { ip: '*******', port: '1111' },
                             2 => { ip: '*******', port: '3333' } } }
            ]
        ]
        tests.each do |a, v, e, b, output|
            expect(backends.resolve(b, a, v, e)).to eq output
        end
    end
end
