# frozen_string_literal: true

require 'rspec'
require 'tmpdir'

def clear_env
    ENV.delete_if { |name| name.start_with?('ETH') || name.include?('VROUTER_') || name.include?('_VNF_') }
end

def clear_vars(object)
    object.instance_variables.each { |name| object.remove_instance_variable(name) }
end

RSpec.describe self do
    it 'should provide and parse all env vars (default networks)' do
        clear_env

        ENV['ONEAPP_VNF_DNS_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_DNS_TCP_DISABLED'] = 'YES'
        ENV['ONEAPP_VNF_DNS_UDP_DISABLED'] = 'NO'

        ENV['ONEAPP_VNF_DNS_UPSTREAM_TIMEOUT'] = '123'
        ENV['ONEAPP_VNF_DNS_MAX_CACHE_TTL'] = '234'

        ENV['ONEAPP_VNF_DNS_USE_ROOTSERVERS'] = 'YES'
        ENV['ONEAPP_VNF_DNS_NAMESERVERS'] = '******* *******'

        ENV['ONEAPP_VNF_DNS_INTERFACES'] = 'eth0 eth1 eth2 eth3'
        ENV['ETH0_VROUTER_MANAGEMENT'] = 'YES'

        ENV['ONEAPP_VNF_DNS_ALLOWED_NETWORKS'] = ''

        ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '***********/16'
        ENV['ONEAPP_VROUTER_ETH0_VIP10'] = '**********/13'
        ENV['ONEAPP_VROUTER_ETH0_VIP1'] = '*******/9'
        ENV['ONEAPP_VROUTER_ETH0_VIP0'] = '*******/5'

        ENV['ETH0_IP'] = '***********'
        ENV['ETH0_MASK'] = '*************'

        ENV['ETH1_IP'] = '***********'
        ENV['ETH1_MASK'] = '***********'

        ENV['ETH2_IP'] = '***********'
        ENV['ETH2_MASK'] = '*********'

        ENV['ETH3_IP'] = '***********'
        ENV['ETH3_MASK'] = '*************'

        load './main.rb'; include Service::DNS

        clear_vars Service::DNS

        expect(Service::DNS.parse_env).to eq ({
            interfaces: { 'eth1' => [ { name: 'eth1', addr: nil, port: nil } ],
                          'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                          'eth3' => [ { name: 'eth3', addr: nil, port: nil } ] },

            nameservers: %w[******* *******],

            networks: %w[*********/16 30.0.0.0/8 **********/24],

            hosts: { 'ip0.eth0'   => '***********',
                     'ip0.eth1'   => '***********',
                     'ip0.eth2'   => '***********',
                     'ip0.eth3'   => '***********',
                     'vip0.eth1'  => '***********',
                     'vip10.eth0' => '**********',
                     'vip1.eth0'  => '*******',
                     'vip0.eth0'  => '*******',
                     'ep0.eth0'   => '*******',
                     'ep10.eth0'  => '**********',
                     'ep1.eth0'   => '*******',
                     'ep0.eth1'   => '***********',
                     'ep0.eth2'   => '***********',
                     'ep0.eth3'   => '***********' }
        })

        output = <<~'UNBOUND_CONF'
            server:
                verbosity: 1

                interface: 127.0.0.1
                interface: eth1
                interface: eth2
                interface: eth3

                unknown-server-time-limit: 123

                do-ip4: yes
                do-ip6: no
                do-udp: yes
                do-tcp: no

                tcp-upstream: no
                udp-upstream-without-downstream: yes

                access-control: 0.0.0.0/0 refuse
                access-control: ::0/0 refuse
                access-control: *********/8 allow
                access-control: *********/16 allow
                access-control: 30.0.0.0/8 allow
                access-control: **********/24 allow

                cache-min-ttl: 0
                cache-max-ttl: 234

                logfile: "/var/log/unbound/unbound.log"
                log-identity: ""
                log-time-ascii: yes
                log-queries: no
                log-replies: no
                log-servfail: yes

                root-hints: /usr/share/dns-root-hints/named.root

                hide-identity: yes
                hide-version: yes

                serve-expired: no

                use-systemd: no
                do-daemonize: yes

                local-zone: "vr." static
                local-data: "ip0.eth0.vr. IN A ***********"
                local-data: "ip0.eth1.vr. IN A ***********"
                local-data: "ip0.eth2.vr. IN A ***********"
                local-data: "ip0.eth3.vr. IN A ***********"
                local-data: "vip0.eth1.vr. IN A ***********"
                local-data: "vip10.eth0.vr. IN A **********"
                local-data: "vip1.eth0.vr. IN A *******"
                local-data: "vip0.eth0.vr. IN A *******"
                local-data: "ep0.eth0.vr. IN A *******"
                local-data: "ep10.eth0.vr. IN A **********"
                local-data: "ep1.eth0.vr. IN A *******"
                local-data: "ep0.eth1.vr. IN A ***********"
                local-data: "ep0.eth2.vr. IN A ***********"
                local-data: "ep0.eth3.vr. IN A ***********"

            remote-control:
                control-enable: no
        UNBOUND_CONF
        Dir.mktmpdir do |dir|
            Service::DNS.configure basedir: dir
            result = File.read "#{dir}/unbound.conf"
            expect(result.strip).to eq output.strip
        end
    end

    it 'should provide and parse all env vars' do
        clear_env

        ENV['ONEAPP_VNF_DNS_ENABLED'] = 'YES'
        ENV['ONEAPP_VNF_DNS_TCP_DISABLED'] = 'YES'
        ENV['ONEAPP_VNF_DNS_UDP_DISABLED'] = 'YES'

        ENV['ONEAPP_VNF_DNS_UPSTREAM_TIMEOUT'] = '123'
        ENV['ONEAPP_VNF_DNS_MAX_CACHE_TTL'] = '234'

        ENV['ONEAPP_VNF_DNS_USE_ROOTSERVERS'] = 'YES'
        ENV['ONEAPP_VNF_DNS_NAMESERVERS'] = '******* *******'

        ENV['ONEAPP_VNF_DNS_INTERFACES'] = 'eth0 eth1 eth1 eth2 eth3/***********'
        ENV['ETH0_VROUTER_MANAGEMENT'] = 'YES'

        ENV['ONEAPP_VNF_DNS_ALLOWED_NETWORKS'] = '*********/16 30.0.0.0/8'

        ENV['ETH0_IP'] = '***********'
        ENV['ETH0_MASK'] = '*************'

        ENV['ETH1_IP'] = '***********'
        ENV['ETH1_MASK'] = '***********'

        ENV['ETH2_IP'] = '***********'
        ENV['ETH2_MASK'] = '*********'

        ENV['ETH3_IP'] = '***********'
        ENV['ETH3_MASK'] = '*************'

        load './main.rb'; include Service::DNS

        clear_vars Service::DNS

        expect(Service::DNS.parse_env).to eq ({
            interfaces: { 'eth1' => [ { name: 'eth1', addr: nil, port: nil },
                                      { name: 'eth1', addr: nil, port: nil } ],
                          'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                          'eth3' => [ { name: 'eth3', addr: '***********', port: nil } ] },

            nameservers: %w[******* *******],

            networks: %w[*********/16 30.0.0.0/8],

            hosts: { 'ip0.eth0' => '***********',
                     'ip0.eth1' => '***********',
                     'ip0.eth2' => '***********',
                     'ip0.eth3' => '***********',
                     'ep0.eth0' => '***********',
                     'ep0.eth1' => '***********',
                     'ep0.eth2' => '***********',
                     'ep0.eth3' => '***********' }
        })
    end

    it 'should fallback to VIPs' do
        clear_env

        ENV['ONEAPP_VNF_DNS_ENABLED'] = 'YES'

        ENV['ONEAPP_VNF_DNS_INTERFACES'] = 'eth0 *******'
        ENV['ETH0_VROUTER_MANAGEMENT'] = 'YES'

        ENV['ONEAPP_VNF_DNS_ALLOWED_NETWORKS'] = ''

        ENV['ONEAPP_VROUTER_ETH1_VIP0'] = '*******/16'

        ENV['ETH0_IP'] = '***********'
        ENV['ETH0_MASK'] = '*************'

        load './main.rb'; include Service::DNS

        clear_vars Service::DNS

        expect(Service::DNS.parse_env).to eq ({
            interfaces: { 'eth1' => [ { name: 'eth1', addr: '*******', port: nil } ] },

            nameservers: %w[],

            networks: %w[*******/16],

            hosts: { 'ip0.eth0'  => '***********',
                     'vip0.eth1' => '*******',
                     'ep0.eth0'  => '***********',
                     'ep0.eth1'  => '*******' }
        })
    end

    it 'should NOT skip networks with inferred netmasks' do
        clear_env

        ENV['ONEAPP_VNF_DNS_ENABLED'] = 'YES'

        ENV['ONEAPP_VNF_DNS_INTERFACES'] = 'eth0 eth1 eth2 eth3 eth4'

        ENV['ETH0_IP'] = '********'
        ENV['ETH1_IP'] = '**********'
        ENV['ETH2_IP'] = '***********'

        ENV['ETH3_IP'] = '*******'
        ENV['ETH4_IP'] = '*******'
        ENV["ETH4_MASK"] = '***********'

        load './main.rb'; include Service::DNS

        clear_vars Service::DNS

        expect(Service::DNS.parse_env).to eq ({
            interfaces: { 'eth0' => [ { name: 'eth0', addr: nil, port: nil } ],
                          'eth1' => [ { name: 'eth1', addr: nil, port: nil } ],
                          'eth2' => [ { name: 'eth2', addr: nil, port: nil } ],
                          'eth3' => [ { name: 'eth3', addr: nil, port: nil } ],
                          'eth4' => [ { name: 'eth4', addr: nil, port: nil } ] },

            nameservers: %w[],

            networks: %w[10.0.0.0/8 **********/16 ***********/24 *******/24 *******/16],

            hosts: { 'ep0.eth0' => '********',
                     'ep0.eth1' => '**********',
                     'ep0.eth2' => '***********',
                     'ep0.eth3' => '*******',
                     'ep0.eth4' => '*******',
                     'ip0.eth0' => '********',
                     'ip0.eth1' => '**********',
                     'ip0.eth2' => '***********',
                     'ip0.eth3' => '*******',
                     'ip0.eth4' => '*******' }
        })
    end
end
