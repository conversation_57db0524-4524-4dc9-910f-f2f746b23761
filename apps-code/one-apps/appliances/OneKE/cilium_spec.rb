# frozen_string_literal: true

require 'base64'
require 'rspec'
require 'tmpdir'
require 'yaml'

require_relative 'helpers.rb'
require_relative 'cilium.rb'

RSpec.describe 'extract_cilium_ranges' do
    it 'should extract and return all ranges (positive)' do
        input = [
            '**********/24',
            '10.11.0.0/16'
        ]
        output = [
            %w[********** 24],
            %w[10.11.0.0 16]
        ]
        expect(extract_cilium_ranges(input)).to eq output
    end

    it 'should extract and return no ranges (negative)' do
        input = [
            '',
            '**********',
            '**********/',
            'asd.11.12.0/24',
            '**********/asd'
        ]
        output = []
        expect(extract_cilium_ranges(input)).to eq output
    end
end

RSpec.describe 'configure_cilium' do
    it 'should apply user-defined ranges' do
        stub_const 'ONEAPP_K8S_CONTROL_PLANE_EP', '**************:6443'
        stub_const 'ONEAPP_K8S_CNI_PLUGIN', 'cilium'
        stub_const 'ONEAPP_K8S_CILIUM_RANGES', ['***************/25', '**********/24']
        output = YAML.load_stream <<~MANIFEST
        ---
        apiVersion: helm.cattle.io/v1
        kind: HelmChartConfig
        metadata:
          name: rke2-cilium
          namespace: kube-system
        spec:
          valuesContent: |-
            kubeProxyReplacement: true
            k8sServiceHost: "**************"
            k8sServicePort: 6443
            cni:
              chainingMode: "none"
              exclusive: false
            bgpControlPlane:
              enabled: false
        MANIFEST
        Dir.mktmpdir do |temp_dir|
            configure_cilium temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/rke2-cilium-config.yaml"
            expect(result).to eq output
        end
    end

    it 'should define ip ranges when ONEAPP_K8S_CILIUM_BGP_ENABLED is true and ONEAPP_K8S_CILIUM_RANGES is not empty' do
        stub_const 'ONEAPP_K8S_CONTROL_PLANE_EP', '**************:6443'
        stub_const 'ONEAPP_K8S_CNI_PLUGIN', 'cilium'
        stub_const 'ONEAPP_K8S_CILIUM_BGP_ENABLED', true
        stub_const 'ONEAPP_K8S_CILIUM_RANGES', ['***************/25', '**********/24']
        output = YAML.load_stream <<~MANIFEST
        ---
        apiVersion: helm.cattle.io/v1
        kind: HelmChartConfig
        metadata:
          name: rke2-cilium
          namespace: kube-system
        spec:
          valuesContent: |-
            kubeProxyReplacement: true
            k8sServiceHost: "**************"
            k8sServicePort: 6443
            cni:
              chainingMode: "none"
              exclusive: false
            bgpControlPlane:
              enabled: true
        ---
        apiVersion: cilium.io/v2alpha1
        kind: CiliumLoadBalancerIPPool
        metadata:
          name: default
          namespace: kube-system
        spec:
          blocks:
          - cidr: ***************/25
          - cidr: **********/24
          allowFirstLastIPs: "No"
        MANIFEST
        Dir.mktmpdir do |temp_dir|
            configure_cilium temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/rke2-cilium-config.yaml"
            expect(result).to eq output
        end
    end

    it 'should define ip ranges when ONEAPP_K8S_CILIUM_BGP_ENABLED is true and ONEAPP_K8S_CILIUM_RANGES is empty' do
        stub_const 'ONEAPP_K8S_CONTROL_PLANE_EP', '**************:6443'
        stub_const 'ONEAPP_K8S_CNI_PLUGIN', 'cilium'
        stub_const 'ONEAPP_K8S_CILIUM_BGP_ENABLED', true
        stub_const 'ONEAPP_K8S_CILIUM_RANGES', []
        output = YAML.load_stream <<~MANIFEST
        ---
        apiVersion: helm.cattle.io/v1
        kind: HelmChartConfig
        metadata:
          name: rke2-cilium
          namespace: kube-system
        spec:
          valuesContent: |-
            kubeProxyReplacement: true
            k8sServiceHost: "**************"
            k8sServicePort: 6443
            cni:
              chainingMode: "none"
              exclusive: false
            bgpControlPlane:
              enabled: true
        ---
        apiVersion: cilium.io/v2alpha1
        kind: CiliumLoadBalancerIPPool
        metadata:
          name: default
          namespace: kube-system
        spec:
          blocks: {}
          allowFirstLastIPs: "No"
        MANIFEST
        Dir.mktmpdir do |temp_dir|
            configure_cilium temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/rke2-cilium-config.yaml"
            expect(result).to eq output
        end
    end

    it 'should apply user-defined config manifest (and ignore user-defined ranges)' do
        manifest = <<~MANIFEST
        ---
        apiVersion: helm.cattle.io/v1
        kind: HelmChartConfig
        metadata:
          name: rke2-cilium
          namespace: kube-system
        spec:
          valuesContent: |-
            kubeProxyReplacement: true
            k8sServiceHost: "**************"
            k8sServicePort: 6443
            cni:
              chainingMode: "none"
              exclusive: false
            bgpControlPlane:
              enabled: true
        ---
        apiVersion: cilium.io/v2alpha1
        kind: CiliumLoadBalancerIPPool
        metadata:
          name: default
          namespace: kube-system
        spec:
          blocks:
          - cidr: ***************/25
          - cidr: **********/24
          allowFirstLastIPs: "No"
        MANIFEST
        stub_const 'ONEAPP_K8S_CNI_PLUGIN', 'cilium'
        stub_const 'ONEAPP_K8S_CNI_CONFIG', Base64.encode64(manifest)
        stub_const 'ONEAPP_K8S_CILIUM_RANGES', ['*******/5', '*******/10']
        output = YAML.load_stream manifest
        Dir.mktmpdir do |temp_dir|
            configure_cilium temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/rke2-cilium-config.yaml"
            expect(result).to eq output
        end
    end
end
