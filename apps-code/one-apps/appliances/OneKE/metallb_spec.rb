# frozen_string_literal: true

require 'base64'
require 'rspec'
require 'tmpdir'
require 'yaml'

require_relative 'helpers.rb'
require_relative 'metallb.rb'

RSpec.describe 'extract_metallb_ranges' do
    it 'should extract and return all ranges (positive)' do
        input = [
            '***********',
            '***********-',
            '***********-***********',
            ' ***********-***********',
            '***********-*********** ',
            '*********** -***********',
            '***********- ***********'
        ]
        output = [
            %w[*********** ***********],
            %w[*********** ***********],
            %w[*********** ***********],
            %w[*********** ***********],
            %w[*********** ***********],
            %w[*********** ***********],
            %w[*********** ***********]
        ]
        expect(extract_metallb_ranges(input)).to eq output
    end

    it 'should extract and return no ranges (negative)' do
        input = [
            '',
            '-***********',
            'asd.11.12.13-***********',
            '***********-***********-************'
        ]
        output = []
        expect(extract_metallb_ranges(input)).to eq output
    end
end

RSpec.describe 'configure_metallb' do
    it 'should apply user-defined ranges (empty)' do
        stub_const 'ONEAPP_K8S_METALLB_CONFIG', nil
        stub_const 'ONEAPP_K8S_METALLB_RANGES', []
        output = YAML.load_stream <<~MANIFEST
        ---
        apiVersion: metallb.io/v1beta1
        kind: IPAddressPool
        metadata:
          name: default
          namespace: metallb-system
        spec:
          addresses: []
        ---
        apiVersion: metallb.io/v1beta1
        kind: L2Advertisement
        metadata:
          name: default
          namespace: metallb-system
        spec:
          ipAddressPools:
          - default
        MANIFEST
        Dir.mktmpdir do |temp_dir|
            configure_metallb temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/one-metallb-config.yaml"
            expect(result).to eq output
        end
    end

    it 'should apply user-defined ranges' do
        stub_const 'ONEAPP_K8S_METALLB_CONFIG', nil
        stub_const 'ONEAPP_K8S_METALLB_RANGES', ['**************-**************']
        output = YAML.load_stream <<~MANIFEST
        ---
        apiVersion: metallb.io/v1beta1
        kind: IPAddressPool
        metadata:
          name: default
          namespace: metallb-system
        spec:
          addresses:
          - **************-**************
        ---
        apiVersion: metallb.io/v1beta1
        kind: L2Advertisement
        metadata:
          name: default
          namespace: metallb-system
        spec:
          ipAddressPools:
          - default
        MANIFEST
        Dir.mktmpdir do |temp_dir|
            configure_metallb temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/one-metallb-config.yaml"
            expect(result).to eq output
        end
    end

    it 'should apply user-defined config manifest (and ignore user-defined ranges)' do
        manifest = <<~MANIFEST
        ---
        apiVersion: metallb.io/v1beta1
        kind: IPAddressPool
        metadata:
          name: default
          namespace: metallb-system
        spec:
          addresses:
          - **************-**************
        ---
        apiVersion: metallb.io/v1beta1
        kind: L2Advertisement
        metadata:
          name: default
          namespace: metallb-system
        spec:
          ipAddressPools:
          - default
        MANIFEST
        stub_const 'ONEAPP_K8S_METALLB_CONFIG', Base64.encode64(manifest)
        stub_const 'ONEAPP_K8S_METALLB_RANGES', ['*******-*******']
        output = YAML.load_stream manifest
        Dir.mktmpdir do |temp_dir|
            configure_metallb temp_dir
            result = YAML.load_stream File.read "#{temp_dir}/one-metallb-config.yaml"
            expect(result).to eq output
        end
    end

end
