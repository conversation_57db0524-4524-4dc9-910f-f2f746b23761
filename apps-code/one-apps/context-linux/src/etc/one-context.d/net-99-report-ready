#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

ENV_FILE=${ENV_FILE:-/var/run/one-context/one_env}
RETRY_COUNT="${RETRY_COUNT:-3}"
RETRY_WAIT_PERIOD="${RETRY_WAIT_PERIOD:-10}"

if [ "$REPORT_READY" != "YES" ]; then
    exit 0
fi

# $TOKENTXT is available only through the env. file
if [ -f "${ENV_FILE}" ]; then
    # shellcheck disable=SC1090
    . "${ENV_FILE}"
fi

###

if ! command -v onegate ; then
    echo "ERROR: No way to signal READY=YES (onegate binary not found)" >&2
    exit 1
fi > /dev/null # this will not drop the error message which goes to stderr

report_ready() {
    onegate vm update --data "READY=YES"
}

is_base64() {
    [[ $1 =~ ^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$ ]]
}

log_ready_error() {
    echo "$1" failed to execute. Not reporting READY on attempt "$RETRY_COUNT" >&2
    false # force fail for retry
}

if [ -n "$READY_SCRIPT_PATH" ]; then
    if [ -e "$READY_SCRIPT_PATH" ]; then
        ready_method() {
            if "$READY_SCRIPT_PATH"; then
                report_ready
            else
                log_ready_error "$READY_SCRIPT_PATH"
            fi
        }
    else
        echo "$READY_SCRIPT_PATH" does not exist >&2
        exit 1
    fi
elif [ -n "$READY_SCRIPT" ]; then
    # Can lead to butchering of simple non encoded commands, Ex. echo
    is_base64 "$READY_SCRIPT" && READY_SCRIPT=$(echo "$READY_SCRIPT" | base64 -d)

    ready_method() {
        if (eval "$READY_SCRIPT"); then
            report_ready
        else
            log_ready_error "READY_SCRIPT"
        fi
    }
else
    ready_method() {
        report_ready
    }
fi

while [ "$RETRY_COUNT" -gt 0 ] ; do
    ready_method

    # shellcheck disable=SC2181
    if [ "$?" = "0" ]; then
        echo "Reported READY"
        exit 0
    fi

    RETRY_COUNT=$(( RETRY_COUNT - 1 ))
    sleep "${RETRY_WAIT_PERIOD}"
done

echo "Failed to report READY" >&2
exit 1
