#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

# THIS IS A CONTEXTUALIZATION GUARD
if [ "$1" != 'local' ] ; then
    exit 0
fi

# arg: <true|yes|false|no>
is_true()
(
    _value=$(echo "$1" | \
        sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//' | \
        tr '[:upper:]' '[:lower:]')
    case "$_value" in
        1|true|yes|y)
            return 0
            ;;
    esac

    return 1
)

activate_swaps_linux() {
    SWAP_DRIVES=$(blkid -t TYPE="swap" -o device)
    for SWAP in $SWAP_DRIVES ; do
        if [ -z "$(swapon -s | grep $SWAP)" ]; then
            swapon "$SWAP"
        fi
    done
}

if is_true "${IGNORE_SWAP}" ; then
    exit 0
fi

if [ "$(uname -s)" = 'Linux' ]; then
    activate_swaps_linux
fi
