#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

# THIS IS A CONTEXTUALIZATION GUARD
if [ "$1" != 'local' ] ; then
    exit 0
fi

ENV_FILE=/var/run/one-context/one_env
MOUNT_DIR="${MOUNT_DIR:-/mnt}"
TOKEN_FILE="${MOUNT_DIR}/token.txt"

if [ -n "$ONEGATE_TOKEN" ]; then
    TOKENTXT="$ONEGATE_TOKEN"
elif [ -f "$TOKEN_FILE" ]; then
    TOKENTXT=$(cat "$TOKEN_FILE")
fi

umask 0377
echo "export TOKENTXT=\"$TOKENTXT\"" > "$ENV_FILE"
echo "export VMID=\"$VMID\"" >> "$ENV_FILE"
echo "export ONEGATE_ENDPOINT=\"$ONEGATE_ENDPOINT\"" >> "$ENV_FILE"

function export_rc_vars
{
  if [ -n "$1" ] && [ -f "$1" ] ; then
    ONE_VARS=$(grep -E -e '^[a-zA-Z\-\_0-9]*=' "$1" | sed 's/=.*$//')

    # shellcheck disable=SC1090
    . "$1"

    for v in $ONE_VARS; do
        echo "export $v=\"${!v}\"" >> "$ENV_FILE"
    done
  fi
}

export_rc_vars "${CONTEXT_FILE}"

chown "root:$(id -gn root)" "$ENV_FILE"
chmod 0400 "$ENV_FILE"

