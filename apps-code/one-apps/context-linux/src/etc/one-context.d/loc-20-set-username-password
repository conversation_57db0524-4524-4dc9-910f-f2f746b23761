#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

# THIS IS A CONTEXTUALIZATION GUARD
if [ "$1" != 'local' ] ; then
    exit 0
fi

# defaults
USERNAME=${USERNAME:-root}
USERNAME_SUDO=${USERNAME_SUDO:-${GRANT_SUDO:-YES}}
USERNAME_SUDO=$(echo "${USERNAME_SUDO}" | tr '[:lower:]' '[:upper:]')
USERNAME_PASSWORD_RESET=${USERNAME_PASSWORD_RESET:-NO}
USERNAME_PASSWORD_RESET=$(echo "${USERNAME_PASSWORD_RESET}" | tr '[:lower:]' '[:upper:]')

_kernel="$(uname -s)"
case "${_kernel}" in
    'FreeBSD')
        USERNAME_SHELL=${USERNAME_SHELL:-/usr/local/bin/bash}
        _sudoers_file='/usr/local/etc/sudoers.d/one-context'
        ;;
    *)
        USERNAME_SHELL=${USERNAME_SHELL:-/bin/bash}
        _sudoers_file='/etc/sudoers.d/one-context'
        ;;
esac

# create user if missing
if ! getent passwd "${USERNAME}" > /dev/null 2>&1; then
    if [ "${_kernel}" = 'FreeBSD' ]; then
        pw user add "${USERNAME}" -m -s "${USERNAME_SHELL}" -w no
    else
        useradd -m "${USERNAME}" -p '*' -s "${USERNAME_SHELL}"
    fi
fi

# enable sudo
if [ "${USERNAME_SUDO}" == "YES" ] && [ "${USERNAME}" != "root" ]; then
    echo "${USERNAME} ALL=(ALL) NOPASSWD:ALL" >"${_sudoers_file}"
    chmod 0440 "${_sudoers_file}"
elif [ -f "${_sudoers_file}" ]; then
    unlink "${_sudoers_file}"
fi

# set password
if [ -n "${CRYPTED_PASSWORD_BASE64}" ]; then
    CRYPTED_PASSWORD=$(echo $CRYPTED_PASSWORD_BASE64 | base64 -d)
    if [ "${_kernel}" = 'FreeBSD' ]; then
        echo "${CRYPTED_PASSWORD}" | pw user mod "${USERNAME}" -H 0
    else
        usermod -p "${CRYPTED_PASSWORD}" "${USERNAME}"
    fi
elif [ -n "${PASSWORD_BASE64}" ]; then
    PASSWORD=$(echo $PASSWORD_BASE64 | base64 -d)
    if [ "${_kernel}" = 'FreeBSD' ]; then
        echo $PASSWORD | pw user mod "${USERNAME}" -h 0
    else
        chpasswd <<< "${USERNAME}:${PASSWORD}"
    fi

    if [ $? -ne 0 ]; then
        passwd "${USERNAME}" <<EOF
${PASSWORD}
${PASSWORD}
EOF
  fi
elif [ -n "${CRYPTED_PASSWORD}" ]; then
    if [ "${_kernel}" = 'FreeBSD' ]; then
        echo $CRYPTED_PASSWORD | pw user mod "${USERNAME}" -H 0
    else
        usermod -p "${CRYPTED_PASSWORD}" "${USERNAME}"
    fi
elif [ -n "${PASSWORD}" ]; then
    if [ "${_kernel}" = 'FreeBSD' ]; then
        echo $PASSWORD | pw user mod "${USERNAME}" -h 0
    else
        chpasswd <<< "${USERNAME}:${PASSWORD}"
    fi

    if [ $? -ne 0 ]; then
        passwd "${USERNAME}" <<EOF
${PASSWORD}
${PASSWORD}
EOF
  fi
elif [ "${USERNAME_PASSWORD_RESET}" = 'YES' ]; then
    if [ "${_kernel}" = 'FreeBSD' ]; then
        pw user mod "${USERNAME}" -w no
    else
        usermod -p '*' "${USERNAME}"
    fi
fi
