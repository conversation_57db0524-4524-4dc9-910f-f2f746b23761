#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

set -e

MOUNT_DIR="${MOUNT_DIR:-/mnt}"
TMP_DIR=$(mktemp -d "${TMP_DIR:-/var/lib/one-context/tmp}/one-context.XXXXXX")

cleanup()
{
    rm -rf "${TMP_DIR}"
}

trap cleanup EXIT

chmod 0700 "${TMP_DIR}"

if [ -z "${INIT_SCRIPTS}" ]; then
    if [ -f "${MOUNT_DIR}/init.sh" ]; then
        INIT_SCRIPTS=init.sh
    fi
fi

cd "${MOUNT_DIR}"

_result=0
for f in $INIT_SCRIPTS; do
    f=$(basename "$f")

    if [ -f "$f" ] ; then
        cp "${f}" "${TMP_DIR}/"
        chmod +x "${TMP_DIR}/${f}"
        "${TMP_DIR}/${f}" || _result=$?
    else
        echo "ERROR: Init script does not exist: ${f}" >&2
        _result=1
    fi
done

exit $_result
