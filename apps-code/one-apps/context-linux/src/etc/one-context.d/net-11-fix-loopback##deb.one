#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

# Checks if the loopback interface is not configured and adds 127.0.0.1.
# This fixes a problem with old Ubuntu 14.04 distros that leave loopback
# IP unconfigured when restarting network. This does not happen with
# Ubuntu 14.04.4

ip_info="$(ip -o -4 address show lo)"

if [ -z "$ip_info" ]; then
    ip addr add 127.0.0.1/8 dev lo
    ip link set lo up
fi
