#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

set -e

MOUNT_DIR=${MOUNT_DIR:-/mnt}
TMP_DIR=$(mktemp -d "${TMP_DIR:-/var/lib/one-context/tmp}/one-context.XXXXXX")
START_SCRIPT_AVAILABLE=no

TMP_FILE="${TMP_DIR}/one-start-script"

cleanup()
{
    rm -rf "${TMP_DIR}"
}

trap cleanup EXIT

chmod 0700 "${TMP_DIR}"

if [ -n "${START_SCRIPT_BASE64}" ]; then
    echo "${START_SCRIPT_BASE64}" | base64 -d > "${TMP_FILE}"
    START_SCRIPT_AVAILABLE=yes
elif [ -n "${START_SCRIPT}" ]; then
    echo "${START_SCRIPT}" > "${TMP_FILE}"
    START_SCRIPT_AVAILABLE=yes
fi

if [ "${START_SCRIPT_AVAILABLE}" = "yes" ]; then
    cd "${MOUNT_DIR}"
    chmod +x "${TMP_FILE}"
    "${TMP_FILE}"
fi
