#!/bin/bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

### BEGIN INIT INFO
# Provides:          one-context
# Required-Start:    $local_fs $network $syslog one-context-local
# Required-Stop:
# Should-Start:      $time
# Should-Stop:
# X-Start-Before:    sshd
# X-Stop-After:
# Default-Start:     2 3 4 5
# Default-Stop:      0 1 6
# Short-Description: OpenNebula contextualization
### END INIT INFO

. /lib/lsb/init-functions

case "$1" in
    start)
        log_daemon_msg $"Starting OpenNebula contextualization"
        /usr/sbin/one-contextd network
        log_end_msg $?
        ;;
    *)
        log_action_msg $"Usage: $0 {start}"
        exit 2
esac
