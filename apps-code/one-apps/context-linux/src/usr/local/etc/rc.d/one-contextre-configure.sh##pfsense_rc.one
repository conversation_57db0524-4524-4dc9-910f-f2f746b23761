#!/bin/sh

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

### BEGIN INIT INFO
# REQUIRE: one-context-local one-context
# PROVIDE: one-context-reconfigure
### END INIT INFO

. /etc/rc.subr

name="one_context_reconfigure"
desc="OpenNebula reconfiguration"
rcvar="${name}_enable"

command="/usr/sbin/one-context-run"

load_rc_config $name
: ${one_context_reconfigure_enable:="yes"}
run_rc_command "$1"


