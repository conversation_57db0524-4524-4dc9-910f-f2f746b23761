#!/usr/bin/env bash

# -------------------------------------------------------------------------- #
# Copyright 2002-2021, OpenNebula Project, OpenNebula Systems                #
#                                                                            #
# Licensed under the Apache License, Version 2.0 (the "License"); you may    #
# not use this file except in compliance with the License. You may obtain    #
# a copy of the License at                                                   #
#                                                                            #
# http://www.apache.org/licenses/LICENSE-2.0                                 #
#                                                                            #
# Unless required by applicable law or agreed to in writing, software        #
# distributed under the License is distributed on an "AS IS" BASIS,          #
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.   #
# See the License for the specific language governing permissions and        #
# limitations under the License.                                             #
#--------------------------------------------------------------------------- #

# Set PATH
export PATH=/sbin:/bin:/usr/sbin:/usr/bin:/usr/local/sbin:/usr/local/bin

TIMEOUT=${TIMEOUT:-30}
COMMAND=${1:-reconfigure}
CONTEXT_BASE=${CONTEXT_BASE:-/var/run/one-context/context.sh}

if [ -f ${CONTEXT_BASE}.local ] && [ -f ${CONTEXT_BASE}.network ]; then
    if [ ${TIMEOUT} -gt 0 ]; then
        (
            set -e
            sleep "${TIMEOUT}"
            /usr/sbin/one-contextd all ${COMMAND}
        ) &>/dev/null &
        disown
    else
        /usr/sbin/one-contextd all ${COMMAND}
    fi
else
    echo 'Contextualization must start first as a boot service' >&2
    exit 1
fi
