[Unit]
Description=OpenNebula contextualization
Requires=one-context-local.service one-context-online.service
After=multi-user.target one-context-local.service one-context-online.service
Wants=network-online.target
After=network-online.target
ConditionPathExists=!/var/run/one-context/context.sh.network

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/sbin/one-contextd network

[Install]
WantedBy=multi-user.target
