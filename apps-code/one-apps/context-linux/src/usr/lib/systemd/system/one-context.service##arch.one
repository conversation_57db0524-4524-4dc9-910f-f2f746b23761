[Unit]
Description=Context script for open nebula
# dbus.service can be dropped once on kdbus, and systemd-udevd.service can be
# dropped once tuntap is moved to netlink
After=systemd-udevd.service dbus.service network-pre.target systemd-sysusers.service systemd-sysctl.service
Before=systemd-networkd.service
Wants=network.target systemd-networkd.service

# On kdbus systems we pull in the busname explicitly, because it
# carries policy that allows the daemon to acquire its name.
Wants=org.freedesktop.network1.busname systemd-networkd.service
After=org.freedesktop.network1.busname

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/sbin/one-contextd
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=one-context
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
