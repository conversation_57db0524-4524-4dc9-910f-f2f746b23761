[Unit]
Description=OpenNebula early-networking contextualization
Requires=one-context-local.service
After=one-context-local.service
After=NetworkManager.service systemd-networkd.service
Wants=network.target
Before=network.target
Before=NetworkManager-wait-online.service systemd-networkd-wait-online.service
ConditionPathExists=!/var/run/one-context/context.sh.online

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/usr/sbin/one-contextd online

[Install]
WantedBy=multi-user.target
