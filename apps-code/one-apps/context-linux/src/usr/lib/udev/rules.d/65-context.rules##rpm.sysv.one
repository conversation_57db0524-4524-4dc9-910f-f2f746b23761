# NIC detach workaround for recontextualization on OpenNebula < 5.10.3
# https://github.com/OpenNebula/one/issues/4130
# https://github.com/OpenNebula/one/issues/4194
SUBSYSTEM=="net", ACTION=="remove", \
  ENV{INTERFACE}=="eth*", \
  RUN+="/bin/sh -c 'test -f /var/run/one-context/context.sh.local && echo >> /var/run/one-context/context.sh.local; test -f /var/run/one-context/context.sh.network && echo >> /var/run/one-context/context.sh.network'"

# On NIC hotplug the delayed reconfiguration is triggered.
SUBSYSTEM=="net", ACTION=="add", \
  RUN+="/sbin/service one-context-reconfigure-delayed start"

SUBSYSTEM=="net", ACTION=="remove", \
  RUN+="/sbin/service one-context-reconfigure-delayed start"

# Handle disk resize
SUBSYSTEM=="block", ACTION=="change", \
  ENV{RESIZE}=="1", \
  RUN+="/sbin/service one-context-force start"

SUBSYSTEM=="scsi", ACTION=="change", \
  ENV{SDEV_UA}=="CAPACITY_DATA_HAS_CHANGED", \
  RUN+="/sbin/service one-context-force start"

# Handle swap hot-attach
SUBSYSTEM=="block", ACTION=="add", \
  ENV{ID_FS_TYPE}=="swap", \
  ENV{DM_ACTIVATION}!="1", \
  RUN+="/sbin/service one-context-force start"

# Handle CPU hotplug
SUBSYSTEM=="cpu", ACTION=="add", TEST=="online", ATTR{online}=="0", ATTR{online}="1"
