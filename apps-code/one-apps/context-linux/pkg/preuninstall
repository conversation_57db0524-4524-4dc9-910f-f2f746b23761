#!/usr/bin/env bash

SERVICES=${SERVICES:-one-context one-context-online one-context-local one-context-reconfigure one-context-reconfigure-delayed}
TIMERS=${TIMERS:-one-context-reconfigure.timer}

# Disable services
if which systemctl >/dev/null 2>&1 && [ -d /etc/systemd ]; then
    for S in ${TIMERS} ${SERVICES}; do
        systemctl --no-reload disable "${S}" >/dev/null 2>&1 || :
        systemctl stop "${S}" >/dev/null 2>&1 || :
    done
fi

if which chkconfig >/dev/null 2>&1; then
    for S in ${SERVICES}; do
        chkconfig --del "${S}" >/dev/null 2>&1 || :
    done

elif which update-rc.d >/dev/null 2>&1; then
    for S in ${SERVICES}; do
        update-rc.d -f "${S}" remove >/dev/null 2>&1 || :
    done

elif which rc-update >/dev/null 2>&1; then
    for S in ${SERVICES}; do
        rc-update del "${S}" boot >/dev/null 2>&1 || :
    done
fi
