# context version definition
VERSION := 7.0.0
RELEASE := 0

# log
VERBOSE         := 1
PACKER_LOG      := 0
PACKER_HEADLESS := true

DISTROS_AMD64 := alma8 alma9 \
                 alpine318 alpine319 alpine320 alpine321 \
                 alt10 \
                 amazon2 amazon2023 \
                 debian11 debian12 \
                 devuan4 devuan5 \
                 fedora39 fedora40 fedora41 fedora42\
                 freebsd13 freebsd14 \
                 ol8 ol9 \
                 opensuse15 \
                 rocky8 rocky9 \
                 ubuntu2004 ubuntu2004min \
                 ubuntu2204 ubuntu2204min ubuntu2204oneke \
                 ubuntu2404 ubuntu2404min

DISTROS_ARM64 := alma8.aarch64 alma9.aarch64 \
                 alpine318.aarch64 alpine319.aarch64 alpine320.aarch64 alpine321.aarch64 \
                 debian11.aarch64 debian12.aarch64 \
                 fedora39.aarch64 fedora40.aarch64 fedora41.aarch64 \
                 opensuse15.aarch64 \
                 rocky8.aarch64 rocky9.aarch64 \
                 ubuntu2204.aarch64 ubuntu2404.aarch64

SERVICES_AMD64 := service_Wordpress service_VRouter service_OneKE service_OneKEa \
                  service_Harbor service_MinIO service_Ray service_Dynamo service_Capi service_example

SERVICES_ARM64 := service_VRouter.aarch64 service_Ray.aarch64 service_Dynamo.aarch64

WINDOWS := windows10Home windows10HomeN windows10HomeSingleLanguage \
           windows10Pro windows10ProN \
           windows10ProWorkstations windows10ProWorkstationsN \
           windows10ProEducation windows10ProEducationN \
           windows10Education windows10EducationN \
           windows10Enterprise windows10EnterpriseN \
           windows10EnterpriseLTSC2015 windows10EnterpriseNLTSC2015 \
           windows10EnterpriseLTSC2016 windows10EnterpriseNLTSC2016 \
           windows10EnterpriseLTSC2019 windows10EnterpriseNLTSC2019 \
           windows10EnterpriseLTSC2021 windows10EnterpriseNLTSC2021 \
           windows11Home windows11HomeN windows11HomeSingleLanguage \
           windows11Pro windows11ProN \
           windows11ProWorkstations windows11ProWorkstationsN \
           windows11ProEducation windows11ProEducationN \
           windows11Education windows11EducationN \
           windows11Enterprise windows11EnterpriseN \
           windows11EnterpriseLTSC2024 windows11EnterpriseNLTSC2024 \
           windows2016Essentials \
           windows2016Standard windows2016StandardCore \
           windows2016Datacenter windows2016DatacenterCore \
           windows2019Essentials \
           windows2019Standard windows2019StandardCore \
           windows2019Datacenter windows2019DatacenterCore \
           windows2022Standard windows2022StandardCore \
           windows2022Datacenter windows2022DatacenterCore \
           windows2025Standard windows2025StandardCore \
           windows2025Datacenter windows2025DatacenterCore

DISTROS  := $(DISTROS_AMD64) $(DISTROS_ARM64) $(WINDOWS)
SERVICES := $(SERVICES_AMD64) $(SERVICES_ARM64)

.DEFAULT_GOAL := help

# default directories
DIR_BUILD  := build
DIR_EXPORT := export
$(shell mkdir -p $(DIR_BUILD) $(DIR_EXPORT))

# don't delete exported
.SECONDARY: $(DISTROS:%=$(DIR_EXPORT)/%.qcow2) $(SERVICES:%=$(DIR_EXPORT)/%.qcow2)

.PHONY: context-linux context-windows context-iso help

# this needs to match context-linux/generate-all.sh products
LINUX_CONTEXT_PACKAGES := \
    one-context_$(VERSION)-$(RELEASE).deb \
    one-context-$(VERSION)-$(RELEASE).el8.noarch.rpm \
    one-context-$(VERSION)-$(RELEASE).el9.noarch.rpm \
    one-context-$(VERSION)-$(RELEASE).el10.noarch.rpm \
    one-context-$(VERSION)-$(RELEASE).fc.noarch.rpm \
    one-context-$(VERSION)-$(RELEASE).amzn2.noarch.rpm \
    one-context-$(VERSION)-$(RELEASE).amzn2023.noarch.rpm \
    one-context-$(VERSION)-$(RELEASE).suse.noarch.rpm \
    one-context-$(VERSION)_$(RELEASE).txz \
    one-context-$(VERSION)-alt$(RELEASE).noarch.rpm \
    one-context-$(VERSION)-r$(RELEASE).apk \
    one-context-linux-$(VERSION)-$(RELEASE).iso

LINUX_CONTEXT_PACKAGES_FULL := $(LINUX_CONTEXT_PACKAGES:%=context-linux/out/%)

CONTEXT_LINUX_SOURCES := $(shell find context-linux/src -type f) \
    context-linux/generate-all.sh context-linux/generate.sh context-linux/targets.sh

# this needs to match context-windows/generate-all.sh products
WINDOWS_CONTEXT_PACKAGES := \
    one-context-$(VERSION)-$(RELEASE).msi \
    one-context-$(VERSION)-$(RELEASE).iso

WINDOWS_CONTEXT_PACKAGES_FULL := $(WINDOWS_CONTEXT_PACKAGES:%=context-windows/out/%)

CONTEXT_WINDOWS_SOURCES := $(shell find context-windows/src -type f)

# logging func
INFO=sh -c 'if [ $(VERBOSE) = 1 ]; then echo [INFO] $$1; fi' INFO

# export all variables
export
