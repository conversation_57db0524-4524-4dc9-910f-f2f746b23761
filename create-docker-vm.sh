#!/bin/bash
set -e

# Generic Docker-to-VM Appliance Generator
# Usage: ./create-docker-vm.sh <docker-image> [appliance-name]
#
# Example: ./create-docker-vm.sh pablodelarco/phoenix-rtos-one:latest phoenixrtos

if [ $# -lt 1 ]; then
    echo "Usage: $0 <docker-image> [appliance-name]"
    echo "Example: $0 pablodelarco/phoenix-rtos-one:latest phoenixrtos"
    exit 1
fi

DOCKER_IMAGE="$1"
APPLIANCE_NAME="${2:-$(echo "$1" | sed 's|.*/||' | sed 's|:.*||')}"

echo "🚀 Creating VM appliance for Docker image: $DOCKER_IMAGE"
echo "📦 Appliance name: $APPLIANCE_NAME"

# Create directory structure
APPLIANCE_DIR="appliances/$APPLIANCE_NAME"
PACKER_DIR="apps-code/community-apps/packer/$APPLIANCE_NAME"

mkdir -p "$APPLIANCE_DIR"
mkdir -p "$PACKER_DIR"

echo "📁 Created directories: $APPLIANCE_DIR and $PACKER_DIR"

# Generate appliance.sh
cat > "$APPLIANCE_DIR/appliance.sh" << 'APPLIANCE_EOF'
#!/usr/bin/env bash
set -o errexit -o pipefail -o nounset

# Appliance metadata
ONE_SERVICE_NAME='DOCKER_IMAGE_PLACEHOLDER VM'
ONE_SERVICE_VERSION='1.0.0'
ONE_SERVICE_BUILD=$(date +%s)
ONE_SERVICE_SHORT_DESCRIPTION='Ubuntu VM running DOCKER_IMAGE_PLACEHOLDER'
ONE_SERVICE_RECONFIGURABLE=false

# Context parameters
ONE_SERVICE_PARAMS=(
  'PASSWORD'       'configure'  'Password for root user'               'O|text'
  'SSH_PUBLIC_KEY' 'configure'  'SSH key for root login'               'O|text64'
  'GROW_ROOTFS'    'configure'  'Automatically grow root filesystem'   'O|boolean'
)

# Defaults
PASSWORD="${PASSWORD:-root}"
SSH_PUBLIC_KEY="${SSH_PUBLIC_KEY:-}"
GROW_ROOTFS="${GROW_ROOTFS:-YES}"

service_help() {
  cat <<EOF >/dev/tty1
DOCKER_IMAGE_PLACEHOLDER VM Appliance
Stages:
  install    - not used (built into image)
  configure  - runs once at first boot
EOF
}

service_install() {
  echo "[Docker VM] install not required." >/dev/tty1 || true
}

service_configure() {
  local SENTINEL=/var/lib/one-context/docker-vm-configured
  [[ -e "$SENTINEL" ]] && exit 0
  touch "$SENTINEL"

  exec > /root/startup.log 2>&1

  perform() {
    # Set root password
    if [[ -n "$PASSWORD" && "$PASSWORD" != "root" ]]; then
      echo "root:${PASSWORD}" | chpasswd
    fi

    # Install SSH key
    if [[ -n "$SSH_PUBLIC_KEY" ]]; then
      mkdir -p /root/.ssh
      echo "$SSH_PUBLIC_KEY" > /root/.ssh/authorized_keys
      chmod 600 /root/.ssh/authorized_keys
    fi

    # Grow filesystem
    if [[ "$GROW_ROOTFS" =~ ^(yes|YES)$ ]]; then
      growpart /dev/vda 1 || true
      resize2fs /dev/vda1   || true
    fi

    # Configure DNS
    sed -i 's/^#\?DNS=.*/DNS=8.8.8.8 *******/' /etc/systemd/resolved.conf
    sed -i 's/^#\?FallbackDNS=.*/FallbackDNS=******* *******/' /etc/systemd/resolved.conf
    systemctl restart systemd-resolved

    # Install Docker if not present
    if ! command -v docker >/dev/null 2>&1; then
      echo "Installing Docker..."
      DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends docker-ce docker-ce-cli containerd.io
      systemctl enable --now docker
    else
      systemctl start docker
    fi

    # Create startup script
    cat <<'SCRIPT' > /usr/local/bin/docker-startup.sh
#!/bin/bash
set -e

# Wait for Docker
while ! docker info >/dev/null 2>&1; do
  echo 'Waiting for Docker...'
  sleep 2
done

# Pull and run the container
echo 'Starting DOCKER_IMAGE_PLACEHOLDER...'
exec docker run --rm -it --network host DOCKER_IMAGE_PLACEHOLDER
SCRIPT

    chmod +x /usr/local/bin/docker-startup.sh

    # Configure auto-exec on console
    cat <<'EOF' >> /root/.bash_profile

# Auto-start Docker container on console login
if [ "$(tty)" = "/dev/tty1" ]; then
  clear
  exec /usr/local/bin/docker-startup.sh
fi
EOF
  }

  perform & pid=$!
  
  # Simple spinner
  while kill -0 "$pid" 2>/dev/null; do
    printf "\rConfiguring Docker VM... " >/dev/tty1
    sleep 1
  done
  printf "\rDocker VM ready!        \n" >/dev/tty1
  
  wait $pid
}

case "${1:-help}" in
  install)   service_install ;;
  configure) service_configure ;;
  help|*)    service_help ;;
esac
APPLIANCE_EOF

# Replace placeholders in appliance.sh
sed -i "s|DOCKER_IMAGE_PLACEHOLDER|$DOCKER_IMAGE|g" "$APPLIANCE_DIR/appliance.sh"
chmod +x "$APPLIANCE_DIR/appliance.sh"

echo "✅ Generated appliance.sh"

# Generate metadata.yaml
cat > "$APPLIANCE_DIR/metadata.yaml" << METADATA_EOF
name: '$APPLIANCE_NAME'
version: '1.0'
publisher: 'Community'
description: 'Ubuntu VM running $DOCKER_IMAGE'
short_description: 'Docker VM for $DOCKER_IMAGE'
tags:
  - 'docker'
  - 'ubuntu'
  - 'development'
format: 'qcow2'
creation_time: $(date +%s)
os-id: 'Ubuntu'
os-release: '22.04'
os-arch: 'x86_64'
hypervisor: 'KVM'
size: 2147483648
METADATA_EOF

echo "✅ Generated metadata.yaml"

# Generate Packer configuration
cat > "$PACKER_DIR/$APPLIANCE_NAME.pkr.hcl" << 'PACKER_EOF'
#include "common.pkr.hcl"

source "null" "create_iso" {
  communicator = "none"
}

build {
  name    = "create_iso"
  sources = ["source.null.create_iso"]

  provisioner "shell-local" {
    inline = [
      "cloud-localds ${var.input_dir}/APPLIANCE_NAME_PLACEHOLDER-cloud-init.iso ${var.input_dir}/cloud-init.yml"
    ]
  }
}

source "qemu" "APPLIANCE_NAME_PLACEHOLDER" {
  cpus             = 2
  memory           = 2048
  accelerator      = "kvm"
  
  iso_url          = "https://cloud-images.ubuntu.com/jammy/current/jammy-server-cloudimg-amd64.img"
  iso_checksum     = "sha256:3c35baa64e58e594e523be5c61fa5f18efdfbc1be3d96a4211fd19e0b3f295e0"
  
  headless         = var.headless
  disk_image       = true
  disk_cache       = "unsafe"
  disk_interface   = "virtio"
  net_device       = "virtio-net"
  format           = "qcow2"
  disk_size        = "8G"
  output_directory = var.output_dir

  qemuargs = [
    ["-cdrom", "${var.input_dir}/APPLIANCE_NAME_PLACEHOLDER-cloud-init.iso"],
    ["-serial", "stdio"],
  ]

  ssh_username     = "root"
  ssh_password     = "opennebula"
  ssh_timeout      = "900s"
  shutdown_command = "poweroff"
  vm_name          = "APPLIANCE_NAME_PLACEHOLDER"
}

build {
  name    = "APPLIANCE_NAME_PLACEHOLDER"
  sources = ["source.qemu.APPLIANCE_NAME_PLACEHOLDER"]

  provisioner "shell" {
    inline = [
      "set -eux",
      
      # Prevent initramfs updates during build
      "echo 'exit 0' > /usr/sbin/update-initramfs",
      "chmod +x /usr/sbin/update-initramfs",

      # Basic setup
      "apt-get update -y",
      "DEBIAN_FRONTEND=noninteractive apt-get purge -y cloud-init cloud-initramfs-growroot || true",

      # Install essentials
      "apt-get install -y --no-install-recommends wget curl ca-certificates",
      
      # Install one-context
      "wget -qO /tmp/one-context.deb https://github.com/OpenNebula/one-apps/releases/download/v6.10.0-3/one-context_6.10.0-3.deb",
      "dpkg -i /tmp/one-context.deb || apt-get install -fy --no-install-recommends",
      "rm -f /tmp/one-context.deb",
      "systemctl enable one-context.service",

      # Pre-configure Docker repository
      "apt-get install -y --no-install-recommends gnupg",
      "install -d -m0755 /etc/apt/keyrings",
      "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg",
      "chmod a+r /etc/apt/keyrings/docker.gpg",
      "echo \"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu jammy stable\" | tee /etc/apt/sources.list.d/docker.list",
      "apt-get update -y",

      # Configure auto-login
      "mkdir -p /etc/systemd/system/<EMAIL>.d",
      "cat <<'CONF' >/etc/systemd/system/<EMAIL>.d/autologin.conf\n[Service]\nExecStart=\nExecStart=-/sbin/agetty --autologin root --noclear %I linux\nCONF",
      "systemctl daemon-reload",
      "<NAME_EMAIL> || true",

      "echo 'Docker VM base setup complete'"
    ]
  }

  provisioner "file" {
    source      = "82-configure-context.sh"
    destination = "/tmp/82-configure-context.sh"
  }

  provisioner "shell" {
    execute_command = "sudo -iu root {{.Vars}} bash {{.Path}}"
    scripts = ["/tmp/82-configure-context.sh"]
    expect_disconnect = true
  }

  provisioner "shell" {
    inline = [
      "apt-get autoremove -y",
      "apt-get autoclean", 
      "rm -rf /var/lib/apt/lists/*",
      "rm -rf /tmp/* /var/tmp/*",
      "history -c",
      "sync"
    ]
  }
}
PACKER_EOF

# Replace placeholders in Packer config
sed -i "s|APPLIANCE_NAME_PLACEHOLDER|$APPLIANCE_NAME|g" "$PACKER_DIR/$APPLIANCE_NAME.pkr.hcl"

echo "✅ Generated Packer configuration"

# Copy required files
cp apps-code/community-apps/packer/phoenixrtos/common.pkr.hcl "$PACKER_DIR/"
cp apps-code/community-apps/packer/phoenixrtos/variables.pkr.hcl "$PACKER_DIR/"
cp apps-code/community-apps/packer/phoenixrtos/cloud-init.yml "$PACKER_DIR/"
cp apps-code/community-apps/packer/phoenixrtos/82-configure-context.sh "$PACKER_DIR/"

echo "✅ Copied required Packer files"

# Add to Makefile
MAKEFILE="apps-code/community-apps/Makefile"
if ! grep -q "^$APPLIANCE_NAME:" "$MAKEFILE"; then
    echo "" >> "$MAKEFILE"
    echo "$APPLIANCE_NAME:" >> "$MAKEFILE"
    echo -e "\t\$(MAKE) packer-$APPLIANCE_NAME" >> "$MAKEFILE"
    echo "" >> "$MAKEFILE"
    echo "packer-$APPLIANCE_NAME:" >> "$MAKEFILE"
    echo -e "\tcd packer/$APPLIANCE_NAME && PKR_VAR_input_dir=\$(PWD) PKR_VAR_output_dir=\$(PWD)/../../export PKR_VAR_headless=true packer build $APPLIANCE_NAME.pkr.hcl" >> "$MAKEFILE"
    
    echo "✅ Added build targets to Makefile"
fi

echo ""
echo "🎉 Docker-to-VM appliance '$APPLIANCE_NAME' created successfully!"
echo ""
echo "📋 Next steps:"
echo "   1. Build the appliance: cd apps-code/community-apps && make $APPLIANCE_NAME"
echo "   2. Test the VM: qemu-system-x86_64 -enable-kvm -m 2048 -drive file=export/$APPLIANCE_NAME.qcow2,format=qcow2 -nographic"
echo "   3. Deploy in OpenNebula using the generated appliance files"
echo ""
echo "📁 Generated files:"
echo "   - $APPLIANCE_DIR/appliance.sh"
echo "   - $APPLIANCE_DIR/metadata.yaml" 
echo "   - $PACKER_DIR/$APPLIANCE_NAME.pkr.hcl"
echo ""
