{"version": "3.13.4", "examples": [{"id": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb[1:1]", "description": "mysql is installed", "full_description": "Appliance Certification mysql is installed", "status": "passed", "file_path": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb", "line_number": 11, "run_time": 0.032850101, "pending_message": null}, {"id": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb[1:2]", "description": "mysql service is running", "full_description": "Appliance Certification mysql service is running", "status": "passed", "file_path": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb", "line_number": 19, "run_time": 15.838926584, "pending_message": null}, {"id": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb[1:3]", "description": "check oneapps motd", "full_description": "Appliance Certification check oneapps motd", "status": "passed", "file_path": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb", "line_number": 37, "run_time": 0.072507019, "pending_message": null}, {"id": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb[1:4]", "description": "can connect as root with defined password", "full_description": "Appliance Certification can connect as root with defined password", "status": "passed", "file_path": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb", "line_number": 52, "run_time": 0.034485399, "pending_message": null}, {"id": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb[1:5]", "description": "database exists", "full_description": "Appliance Certification database exists", "status": "passed", "file_path": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb", "line_number": 61, "run_time": 0.031366947, "pending_message": null}, {"id": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb[1:6]", "description": "can connect as user with defined password", "full_description": "Appliance Certification can connect as user with defined password", "status": "passed", "file_path": "/var/lib/one/marketplace-community/appliances/testapp/tests/00-testapp_basic.rb", "line_number": 72, "run_time": 0.030679269, "pending_message": null}], "summary": {"duration": 98.995178674, "example_count": 6, "failure_count": 0, "pending_count": 0, "errors_outside_of_examples_count": 0}, "summary_line": "6 examples, 0 failures"}