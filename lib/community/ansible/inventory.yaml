---
all:
  vars:
    ansible_user: root
    one_version: '6.10'
    one_pass: opennebula
    unattend_disable: true
    ds:
      mode: ssh
    vn:
      admin_net:
        managed: true
        template:
          VN_MAD: bridge
          PHYDEV: eth0
          BRIDGE: br0
          AR:
            TYPE: IP4
            IP: ************
            SIZE: 48
          NETWORK_ADDRESS: **********
          NETWORK_MASK: *************
          GATEWAY: **********
          DNS: *******

frontend:
  hosts:
    f1: { ansible_host: ********** } # replace with the machine that will run opennebula

node:
  hosts:
    n1: { ansible_host: ********** } # # replace with the machine that will run opennebula
