#!/bin/bash
set -e

# Generic Docker-to-VM builder for Phoenix-RTOS
echo "Building Phoenix-RTOS VM using generic Docker-to-VM template..."

cd packer/docker-generic

# Set variables
export PKR_VAR_docker_image="pablodelarco/phoenix-rtos-one:latest"
export PKR_VAR_vm_name="phoenixrtos-generic"
export PKR_VAR_disk_size_mb="8192"
export PKR_VAR_input_dir="$(pwd)"
export PKR_VAR_output_dir="$(pwd)/build"
export PKR_VAR_headless="true"

# Create build directory
mkdir -p build

# Build the VM
echo "Starting Packer build..."
packer build docker-generic.pkr.hcl

echo "Build complete! VM image: build/phoenixrtos-generic"
