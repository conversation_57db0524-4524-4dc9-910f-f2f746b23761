#!/usr/bin/env bash

# Configure one-context
systemctl enable one-context.service

# Move context files if they exist
if [ -f /etc/one-appliance/net-90-service-appliance ]; then
    mv /etc/one-appliance/net-90-service-appliance /etc/one-context.d/
fi
if [ -f /etc/one-appliance/net-99-report-ready ]; then
    mv /etc/one-appliance/net-99-report-ready /etc/one-context.d/
fi

# Ensure proper permissions
chmod +x /etc/one-context.d/* 2>/dev/null || true
