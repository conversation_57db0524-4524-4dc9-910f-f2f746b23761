# Generic Docker-to-VM Packer Template
# This template creates a VM that auto-runs any Docker container

#include "common.pkr.hcl"

# Variables for customization
variable "docker_image" {
  type        = string
  description = "Docker image to run (e.g., 'pablodelarco/phoenix-rtos-one:latest')"
}

variable "vm_name" {
  type        = string
  description = "Name for the VM and appliance"
}

variable "disk_size_mb" {
  type        = number
  default     = 8192
  description = "Disk size in MB"
}

# Build cloud-init ISO
source "null" "create_iso" {
  communicator = "none"
}

build {
  name    = "create_iso"
  sources = ["source.null.create_iso"]

  provisioner "shell-local" {
    inline = [
      "cloud-localds ${var.input_dir}/${var.vm_name}-cloud-init.iso ${var.input_dir}/cloud-init.yml"
    ]
  }
}

# Main VM build
source "qemu" "docker_vm" {
  cpus             = 2
  memory           = 2048
  accelerator      = "kvm"

  iso_url          = "https://cloud-images.ubuntu.com/jammy/current/jammy-server-cloudimg-amd64.img"
  iso_checksum     = "sha256:3c35baa64e58e594e523be5c61fa5f18efdfbc1be3d96a4211fd19e0b3f295e0"

  headless         = var.headless
  disk_image       = true
  disk_cache       = "unsafe"
  disk_interface   = "virtio"
  net_device       = "virtio-net"
  format           = "qcow2"
  disk_size        = var.disk_size_mb
  output_directory = var.output_dir

  qemuargs = [
    ["-cdrom", "${var.input_dir}/${var.vm_name}-cloud-init.iso"],
    ["-serial", "stdio"],
  ]

  ssh_username     = "root"
  ssh_password     = "opennebula"
  ssh_timeout      = "900s"
  shutdown_command = "poweroff"
  vm_name          = var.vm_name
}

build {
  name    = "docker_vm"
  sources = ["source.qemu.docker_vm"]

  provisioner "shell" {
    inline = [
      "set -eux",

      # Prevent initramfs updates during build to save space
      "echo 'exit 0' > /usr/sbin/update-initramfs",
      "chmod +x /usr/sbin/update-initramfs",

      # Basic system setup
      "apt-get update -y",
      "DEBIAN_FRONTEND=noninteractive apt-get purge -y cloud-init cloud-initramfs-growroot || true",

      # Install essential packages
      "apt-get install -y --no-install-recommends wget curl ca-certificates",

      # Install one-context
      "wget -qO /tmp/one-context.deb https://github.com/OpenNebula/one-apps/releases/download/v6.10.0-3/one-context_6.10.0-3.deb",
      "dpkg -i /tmp/one-context.deb || apt-get install -fy --no-install-recommends",
      "rm -f /tmp/one-context.deb",
      "systemctl enable one-context.service",

      # Pre-configure Docker repository
      "apt-get install -y --no-install-recommends gnupg",
      "install -d -m0755 /etc/apt/keyrings",
      "curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg",
      "chmod a+r /etc/apt/keyrings/docker.gpg",
      "echo \"deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu jammy stable\" | tee /etc/apt/sources.list.d/docker.list",
      "apt-get update -y",

      # Configure auto-login
      "mkdir -p /etc/systemd/system/<EMAIL>.d",
      "cat <<'CONF' >/etc/systemd/system/<EMAIL>.d/autologin.conf\n[Service]\nExecStart=\nExecStart=-/sbin/agetty --autologin root --noclear %I linux\nCONF",
      "systemctl daemon-reload",
      "<NAME_EMAIL> || true",

      # Create startup script that will install Docker and run the container
      "cat <<'SCRIPT' > /usr/local/bin/docker-startup.sh",
      "#!/bin/bash",
      "set -e",
      "",
      "# Install Docker if not present",
      "if ! command -v docker >/dev/null 2>&1; then",
      "  echo 'Installing Docker...'",
      "  DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends docker-ce docker-ce-cli containerd.io",
      "  systemctl enable --now docker",
      "  echo 'Docker installed successfully'",
      "fi",
      "",
      "# Wait for Docker to be ready",
      "while ! docker info >/dev/null 2>&1; do",
      "  echo 'Waiting for Docker...'",
      "  sleep 2",
      "done",
      "",
      "# Pull and run the Docker image",
      "echo 'Starting ${var.docker_image}...'",
      "exec docker run --rm -it --network host ${var.docker_image}",
      "SCRIPT",
      "",
      "chmod +x /usr/local/bin/docker-startup.sh",

      # Configure root to run the startup script on login
      "cat <<'EOF' >> /root/.bash_profile",
      "",
      "# Auto-start Docker container on console login",
      "if [ \"$(tty)\" = \"/dev/tty1\" ]; then",
      "  clear",
      "  exec /usr/local/bin/docker-startup.sh",
      "fi",
      "EOF",

      "echo 'Generic Docker VM setup complete'"
    ]
  }

  # Copy context configuration
  provisioner "file" {
    source      = "82-configure-context.sh"
    destination = "/tmp/82-configure-context.sh"
  }

  provisioner "shell" {
    execute_command = "sudo -iu root {{.Vars}} bash {{.Path}}"
    scripts = [
      "/tmp/82-configure-context.sh"
    ]
    expect_disconnect = true
  }

  # Cleanup and optimize
  provisioner "shell" {
    inline = [
      "apt-get autoremove -y",
      "apt-get autoclean",
      "rm -rf /var/lib/apt/lists/*",
      "rm -rf /tmp/*",
      "rm -rf /var/tmp/*",
      "history -c",
      "sync"
    ]
  }
}