### Description
Please, make sure the following items requirements are met within your PR:

 - [ ] A Packer template file is provided
 - [ ] the PR is linked to proper branch with a new appliance contribution
 - [ ] A YAML file with appliance metadata is provided (check https://github.com/OpenNebula/marketplace?tab=readme-ov-file#image-with-optional-vm-template for more details)
 - [ ] The appliance specific tests are present in the branch
 - [ ] The appliance documentation (i.e. the text to be inserted into https://github.com/OpenNebula/marketplace-community/wiki/community_apps similar to Lithops, UERANSIM, etc)

### Branches to which this PR applies

<!--- Please check you didn't forget a branch this needs to be cherry picked to.
      Leave them unchecked, they will be checked by the merger --->

- [ ] master
- [ ] one-X.X

<hr>

- [ ] Check this if this PR should **not** be squashed
