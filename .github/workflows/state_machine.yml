name: Process Issue State

on:
  pull_request_target:
    types:
      - labeled

jobs:
  do-changes:
    if: startsWith(github.event.label.name, 'do_changes')
    runs-on: ubuntu-latest
    steps:
      - name: Update labels
        run: |
          gh pr edit ${{ github.event.pull_request.number }} --repo ${{ github.repository_owner }}/${{ github.event.repository.name }} --add-label "CHANGES_REQUESTED"
          gh pr edit ${{ github.event.pull_request.number }} --repo ${{ github.repository_owner }}/${{ github.event.repository.name }} --remove-label "do_changes,FAILED_TESTING"
        env:
          GH_TOKEN: ${{ github.token }}

  do-tests:
    if: startsWith(github.event.label.name, 'do_tests')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout PR code
        uses: actions/checkout@v3
        with:
          repository: ${{ github.event.pull_request.head.repo.full_name }}
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Configure SSH
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
        run: |
          mkdir -p ~/.ssh
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          echo -e "Host *\n\tStrictHostKeyChecking no\n" > ~/.ssh/config

      - name: Install yq
        run: |
          sudo apt-get update
          sudo apt-get install yq

      - name: Trigger Jenkins
        env:
          VPN_SERVER: ${{ secrets.VPN_SERVER }}
          JENKINS_URL: ${{ secrets.JENKINS_URL }}
          ONE_DISTRO_TOKEN: ${{ secrets.ONE_DISTRO_TOKEN }}
          JENKINS_USER: ${{ secrets.JENKINS_USER }}
          JENKINS_TOKEN: ${{ secrets.JENKINS_TOKEN }}
        run: |
          METADATA_PATH="appliances/${{ github.event.pull_request.title }}/metadata.yaml"
          DISTRO=$(yq '.[":app"][":name"]' $METADATA_PATH)
          REQUIREMENTS=$(yq '.[":app"][":os"][":base"]' $METADATA_PATH)
          ssh -i ~/.ssh/id_rsa gitact@$VPN_SERVER -p 2222 "curl -X POST $JENKINS_URL/job/one-community-distro/buildWithParameters?token=$ONE_DISTRO_TOKEN \
            --user $JENKINS_USER:$JENKINS_TOKEN \
            --data DISTROS=$DISTRO \
            --data REQUIREMENTS=$REQUIREMENTS \
            --data RUN_CONTEXT=true \
            --data PR_NUMBER=${{ github.event.pull_request.number }}"
            
      - name: Update labels
        run: |
          gh pr edit ${{ github.event.pull_request.number }} --repo ${{ github.repository_owner }}/${{ github.event.repository.name }} --add-label "IN_TESTING"
          gh pr edit ${{ github.event.pull_request.number }} --repo ${{ github.repository_owner }}/${{ github.event.repository.name }} --remove-label "do_tests,CHANGES_REQUESTED"
        env:
          GH_TOKEN: ${{ github.token }}
