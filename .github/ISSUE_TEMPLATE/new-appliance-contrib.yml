name: New Appliance Contribution Request
description: New Appliance Contribution Request Form
title: "[New Appliance]: "
body:
  - type: textarea
    id: details
    attributes:
      label: Appliance Details
      description: Describe the nature, objective, structure, and requirements of your appliance
    validations:
      required: true
  - type: checkboxes
    id: versions
    attributes:
      label: Appliance Compatibility
      description: Please, specify OpenNebula versions the new appliance is compatible with
      options:
        - label: "6.10"
          required: true
  - type: textarea
    id: documentation
    attributes:
      label: Appliance Documentation
      description: Please, provide comprehensive documentation including installation guides, user manuals, and troubleshooting information
    validations:
      required: true
  - type: textarea
    id: vendor
    attributes:
      label: Vendor Information
      description: Please, supply details about your company or organization
    validations:
      required: false
  - type: markdown
    attributes:
      value: |
       *Steps to contribute:*
          1. Submit the Form: Fill out all sections of the form with the required information.
          2. Initial Review: Our team will review your submission for completeness and initial compliance.
          3. Certification Testing: Your appliance will undergo compatibility, performance, and security testing.
          4. Documentation Review: We will review your documentation for clarity and thoroughness.
          5. Feedback: If needed, we will provide feedback for revisions.
          6. Final Approval: Once approved, your appliance will be published in the Community Marketplace.

        
        Thank you for your contribution and commitment to enhancing the OpenNebula ecosystem! If you have any questions or need assistance, please, contact our support team. 
