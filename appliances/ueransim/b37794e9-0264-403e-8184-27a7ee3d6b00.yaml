---
name: UERANSIM
version: 6.10.0-3-20250404
publisher: OpenNebula Systems
description: |-
  Appliance with a pre-installed version of the [UERANSIM](https://github.com/aligungr/UERANSIM) 5G UE and RAN (gNodeB) simulator.

  See the dedicated [documentation](https://github.com/OpenNebula/marketplace-community/wiki/ueransim_quick).
short_description: Appliance with preinstalled UERANSIM for KVM hosts
tags:
- ueransim
- ubuntu
- service
format: qcow2
creation_time: 1743762026
os-id: Ubuntu
os-release: 22.04 LTS
os-arch: x86_64
hypervisor: KVM
opennebula_version: 6.4, 6.6, 6.8, 6.10, 7.0
opennebula_template:
  context:
    network: 'YES'
    ssh_public_key: "$USER[SSH_PUBLIC_KEY]"
    set_hostname: "$NAME"
    oneapp_ueran_network_mcc: "$ONEAPP_UERAN_NETWORK_MCC"
    oneapp_ueran_network_mnc: "$ONEAPP_UERAN_NETWORK_MNC"
    oneapp_ueran_cell_id: "$ONEAPP_UERAN_CELL_ID"
    oneapp_ueran_gnb_id: "$ONEAPP_UERAN_GNB_ID"
    oneapp_ueran_tac_id: "$ONEAPP_UERAN_TAC_ID"
    oneapp_ueran_amf_ip: "$ONEAPP_UERAN_AMF_IP"
    oneapp_ueran_amf_port: "$ONEAPP_UERAN_AMF_PORT"
    oneapp_ueran_sst_id: "$ONEAPP_UERAN_SST_ID"
    oneapp_ueran_ue_imsi: "$ONEAPP_UERAN_UE_IMSI"
    oneapp_ueran_subscription_key: "$ONEAPP_UERAN_SUBSCRIPTION_KEY"
    oneapp_ueran_operator_code: "$ONEAPP_UERAN_OPERATOR_CODE"
    oneapp_ueran_vnf_ip: "$ONEAPP_UERAN_VNF_IP"
    oneapp_ueran_core_subnet: "$ONEAPP_UERAN_CORE_SUBNET"
  cpu: '2'
  graphics:
    listen: 0.0.0.0
    type: vnc
  inputs_order: ''
  memory: '2048'
  os:
    arch: x86_64
  user_inputs:
    oneapp_ueran_network_mcc: O|text|Mobile Country Code value| |922
    oneapp_ueran_network_mnc: O|text|Mobile Network Code value (2 or 3 digits)| |77
    oneapp_ueran_cell_id: O|text|NR Cell Identity (36-bit)| |0x000000010
    oneapp_ueran_gnb_id: O|text|NR gNB ID length in bits [22...32]| |32
    oneapp_ueran_tac_id: O|text|Tracking Area Code| |1
    oneapp_ueran_amf_ip: O|text|AMF IP Address| |127.0.0.1
    oneapp_ueran_amf_port: O|text|AMF_PORT| |38412
    oneapp_ueran_sst_id: O|text|List of supported S-NSSAIs by this gNB slices| |1
    oneapp_ueran_ue_imsi: >-
      O|text|IMSI number of the UE. IMSI = [MCC,MNC,MSISDN] (In total 15 digits)| |imsi-999700000000001
    oneapp_ueran_subscription_key: O|text|Permanent subscription key| |465B5CE8B199B49FAA5F0A2EE238A6BC
    oneapp_ueran_operator_code: >-
      O|text|Operator code (OP or OPC) of the UE| |E8ED289DEBA952E4283B54E88E6183CA
    oneapp_ueran_vnf_ip: O|text|IP of the virtual router to connect to the OneKE cluster| |1
    oneapp_ueran_core_subnet: O|text|Subnet assigned to the 5G core services| |1
logo: ueransim.png
images:
- name: ueransim
  url: >-
    https://opennebula-marketplace.s3.amazonaws.com/service_ueransim-6.10.0-3-20250404.qcow2
  type: OS
  dev_prefix: vd
  driver: qcow2
  size: 10737418240
  checksum:
    md5: 00cf1ffec82d25794f8174554296be77
    sha256: ae1b8e9d0d0270d4ca90afc63668426fd9ab01f3ad7931438cba142f37db7bfd
