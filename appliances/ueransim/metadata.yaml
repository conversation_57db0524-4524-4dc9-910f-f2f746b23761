---
:app:
  :name: ueransim # name used to make the app with the makefile
  :type: service # there are service (complex apps) and distro (base apps)
  :os:
    :type: linux # linux, freebsd or windows
    :base: ubuntu2204 # distro where the app runs on
  :hypervisor: KVM
  :context: # which context params are used to control the app
    :prefixed: true # params are prefixed with ONEAPP_
    :params:
      :UERAN_NETWORK_MCC: '922'
      :UERAN_NETWORK_MNC: '77'
      :UERAN_CELL_ID: '0x000000010'
      :UERAN_GNB_ID: '32'
      :UERAN_TAC_ID: '1'
      :UERAN_AMF_IP: '127.0.0.1'
      :UERAN_AMF_PORT: '38412'
      :UERAN_SST_ID: '1'
      :UERAN_UE_IMSI: 'imsi-999700000000001'
      :UERAN_SUBSCRIPTION_KEY: '465B5CE8B199B49FAA5F0A2EE238A6BC'
      :UERAN_OPERATOR_CODE: 'E8ED289DEBA952E4283B54E88E6183CA'
      :UERAN_VNF_IP: '1'
      :UERAN_CORE_SUBNET: '1'


:one:
  # reference template for running the virtual appliance.
  # Must be diskless, with ssh access and accessible by  the frontend
  :template: # how the VM template is expected to be on OpenNebula
    NAME: base
    TEMPLATE:
      ARCH: x86_64
      CONTEXT:
        NETWORK: 'YES'
        SSH_PUBLIC_KEY: "$USER[SSH_PUBLIC_KEY]"
      CPU: '0.1'
      CPU_MODEL:
        MODEL: host-passthrough
      GRAPHICS:
        LISTEN: 0.0.0.0
        TYPE: vnc
      MEMORY: '2048'
      NIC:
        NETWORK: service
      NIC_DEFAULT:
        MODEL: virtio
  :datastore_name: default # target datatore to import the one-apps produced image
  :timeout: '90' # timeout for XMLRPC calls

:infra:
  :disk_format: qcow2 # oneapps built image disk format
  :apps_path: /var/tmp # directory where one-apps exports the appliances to
