---
name: NixOS
version: 25.05.803297.10d7f8d34e5e-20250609
publisher: OpenNebula Systems
description: NixOS 25.05 (cloud-init)
short_description: NixOS 25.05
tags:
- nixos
format: qcow2
creation_time: 1749473169
os-id: NixOS
os-release: 25.05
os-arch: x86_64
hypervisor: ALL
opennebula_version: 6.10, 7.0
opennebula_template:
  context:
    network: 'YES'
    set_hostname: "$NAME"
    ssh_public_key: "$USER[SSH_PUBLIC_KEY]"
    token: 'YES'
  cpu: '1'
  graphics:
    listen: 0.0.0.0
    type: vnc
  memory: '3072'
  lxd_security_privileged: 'true'
  os:
    arch: x86_64
    firmware_secure: 'YES'
  vcpu: '2'
logo: nixos.png
images:
- name: nixos_os
  url: https://d24fmfybwxpuhu.cloudfront.net/nixos-25.05.803297.10d7f8d34e5e-20250609.qcow2
  type: OS
  dev_prefix: vd
  driver: qcow2
  size: 10737418240
  checksum:
    md5: 2f3ad06c113ccb65d4855efc29a26c3d
    sha256: 57d251d21086acc80128bfc6b7d67f2c616e2511e12cb37b25494a2974febe96
