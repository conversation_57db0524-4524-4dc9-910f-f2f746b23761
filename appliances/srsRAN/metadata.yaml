---
:app:
  :name: srsRA<PERSON> # name used to make the app with the makefile
  :type: service # there are service (complex apps) and distro (base apps)
  :os:
    :type: linux # linux, freebsd or windows
    :base: ubuntu24 # distro where the app runs on
  :hypervisor: KVM
  :context: # which context params are used to control the app
    :prefixed: true # params are prefixed with ONEAPP_
    :params:
      :SRSRAN_MODE: 'gnb'
      :SRSRAN_MCC: '999'
      :SRSRAN_MNC: '75'
      :SRSRAN_TAC: '1'
      :SRSRAN_PCI: '69'
      :SRSRAN_DL_ARFCN: '656668'
      :SRSRAN_BAND: 'n77'
      :SRSRAN_CHANNEL_BW_MHZ: '100'
      :SRSRAN_ENABLE_DPDK: 'YES'
      :SRSRAN_COMMON_SCS: '30'
      :SRSRAN_NR_CELLS: '1'
      :SRSRAN_AMF_IPV4: '********'
      :SRSRAN_NIC_PCI_ADDR: '0000:01:01.0'
      :SRSRAN_RU_MAC: 'e8:c7:4f:25:89:41'

:one:
  # reference template for running the virtual appliance.
  # Must be diskless, with ssh access and accessible by  the frontend
  :template: # how the VM template is expected to be on OpenNebula
    NAME: srsRAN
    TEMPLATE:
      ARCH: x86_64
      CONTEXT:
        NETWORK: 'YES'
        SSH_PUBLIC_KEY: "$USER[SSH_PUBLIC_KEY]"
      CPU: '32'
      CPU_MODEL:
        MODEL: host-passthrough
      GRAPHICS:
        LISTEN: 0.0.0.0
        TYPE: vnc
      MEMORY: '32768'
      NIC:
        NETWORK: service
      NIC_DEFAULT:
        MODEL: virtio
  :datastore_name: default # target datatore to import the one-apps produced image
  :timeout: '90' # timeout for XMLRPC calls

:infra:
  :disk_format: qcow2 # oneapps built image disk format
  :apps_path: /var/tmp # directory where one-apps exports the appliances to
