#!/usr/bin/env bash

# Docker-based appliance that pre-pulls Phoenix RTOS container image
# This appliance installs Docker and pre-pulls the specified Docker image during build time

### DOCKER PHOENIX APPLIANCE ##########################################################

# For organization purposes is good to define here variables that will be used by your bash logic
DOCKER_IMAGE="pablodelarco/phoenix-rtos-one:latest"
ONE_SERVICE_SETUP_DIR="/opt/one-appliance" ### Install location. Required by bash helpers

### CONTEXT SECTION ##########################################################

# List of contextualization parameters
# This is how you interact with the appliance using OpenNebula.
# These variables are defined in the CONTEXT section of the VM Template as custom variables
# https://docs.opennebula.io/6.8/management_and_operations/references/template.html#context-section
ONE_SERVICE_PARAMS=(
    'ONEAPP_DOCKER_IMAGE'       'configure' 'Docker image to run'                              ''
    'ONEAPP_DOCKER_ARGS'        'configure' 'Additional Docker run arguments'                  ''
    'ONEAPP_AUTO_START'         'configure' 'Auto-start container on boot'                     ''
)
# Default values for when the variable doesn't exist on the VM Template
ONEAPP_DOCKER_IMAGE="${ONEAPP_DOCKER_IMAGE:-${DOCKER_IMAGE}}"
ONEAPP_DOCKER_ARGS="${ONEAPP_DOCKER_ARGS:---rm -it --network host}"
ONEAPP_AUTO_START="${ONEAPP_AUTO_START:-yes}"

# You can make this parameters a required step of the VM instantiation wizard by using the USER_INPUTS feature
# https://docs.opennebula.io/6.8/management_and_operations/vm_management/vm_templates.html?#user-inputs

###############################################################################
###############################################################################
###############################################################################

# The following functions will be called by the appliance service manager at
# the  different stages of the appliance life cycles. They must exist
# https://github.com/OpenNebula/one-apps/wiki/apps_intro#appliance-life-cycle

#
# Mandatory Functions
#

service_install()
{
    mkdir -p "$ONE_SERVICE_SETUP_DIR"

    msg info "Update package repositories"
    if ! apt-get update ; then
        msg error "Failed to update package repositories"
        exit 1
    fi

    msg info "Install Docker prerequisites"
    if ! apt-get install -y --no-install-recommends ca-certificates curl gnupg lsb-release ; then
        msg error "Failed to install Docker prerequisites"
        exit 1
    fi

    msg info "Add Docker GPG key"
    install -m 0755 -d /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    chmod a+r /etc/apt/keyrings/docker.gpg

    msg info "Add Docker repository"
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

    msg info "Update package repositories with Docker repo"
    if ! apt-get update ; then
        msg error "Failed to update package repositories"
        exit 1
    fi

    msg info "Install Docker"
    if ! apt-get install -y --no-install-recommends docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin ; then
        msg error "Docker installation failed"
        exit 1
    fi

    msg info "Enable Docker service"
    systemctl enable docker

    msg info "Start Docker service"
    systemctl start docker

    msg info "Pre-pull Docker image: ${DOCKER_IMAGE}"
    if ! docker pull "${DOCKER_IMAGE}" ; then
        msg error "Failed to pull Docker image: ${DOCKER_IMAGE}"
        exit 1
    fi

    msg info "Clean up package cache"
    apt-get autoremove -y
    apt-get autoclean
    rm -rf /var/lib/apt/lists/*

    msg info "INSTALLATION FINISHED"

    return 0
}

service_configure()
{
    msg info "Configure Docker service"

    # Ensure Docker is running
    systemctl enable docker
    systemctl start docker

    # Wait for Docker to be ready
    msg info "Waiting for Docker to be ready..."
    while ! docker info >/dev/null 2>&1; do
        sleep 2
    done

    msg info "Verify Docker image is available"
    if ! docker images "${ONEAPP_DOCKER_IMAGE}" --format "table {{.Repository}}:{{.Tag}}" | grep -q "${ONEAPP_DOCKER_IMAGE}"; then
        msg info "Pulling Docker image: ${ONEAPP_DOCKER_IMAGE}"
        if ! docker pull "${ONEAPP_DOCKER_IMAGE}"; then
            msg error "Failed to pull Docker image: ${ONEAPP_DOCKER_IMAGE}"
            exit 1
        fi
    fi

    msg info "Create Docker startup script"
    cat > /usr/local/bin/docker-phoenix-start.sh <<EOF
#!/bin/bash
set -e

# Wait for Docker to be ready
while ! docker info >/dev/null 2>&1; do
    echo "Waiting for Docker..."
    sleep 2
done

echo "Starting Docker container: ${ONEAPP_DOCKER_IMAGE}"
exec docker run ${ONEAPP_DOCKER_ARGS} "${ONEAPP_DOCKER_IMAGE}"
EOF

    chmod +x /usr/local/bin/docker-phoenix-start.sh

    msg info "Configuration and service info saved in: ${ONE_SERVICE_REPORT}"

    cat > "$ONE_SERVICE_REPORT" <<EOF
[Docker Phoenix RTOS Appliance]
Docker Image: ${ONEAPP_DOCKER_IMAGE}
Docker Args:  ${ONEAPP_DOCKER_ARGS}
Auto Start:   ${ONEAPP_AUTO_START}

[Usage]
To start the container manually:
  /usr/local/bin/docker-phoenix-start.sh

To run with custom arguments:
  docker run ${ONEAPP_DOCKER_ARGS} ${ONEAPP_DOCKER_IMAGE}
EOF

    chmod 600 "$ONE_SERVICE_REPORT"

    msg info "CONFIGURATION FINISHED"

    return 0
}

service_bootstrap()
{
    msg info "Bootstrap Docker Phoenix RTOS appliance"

    # Configure auto-start if enabled
    if [ "${ONEAPP_AUTO_START}" = "yes" ]; then
        msg info "Configuring auto-start for Docker container"

        # Create systemd service for auto-start
        cat > /etc/systemd/system/docker-phoenix.service <<EOF
[Unit]
Description=Docker Phoenix RTOS Container
After=docker.service
Requires=docker.service

[Service]
Type=simple
ExecStart=/usr/local/bin/docker-phoenix-start.sh
Restart=always
RestartSec=10
User=root

[Install]
WantedBy=multi-user.target
EOF

        systemctl daemon-reload
        systemctl enable docker-phoenix.service

        msg info "Auto-start service configured"
    fi

    msg info "BOOTSTRAP FINISHED"

    return 0
}

# This one is not really mandatory, however it is a handled function
service_help()
{
    msg info "Docker Phoenix RTOS Appliance"
    msg info "This appliance runs Phoenix RTOS in a Docker container"
    msg info ""
    msg info "Configuration parameters:"
    msg info "  ONEAPP_DOCKER_IMAGE: Docker image to run (default: ${DOCKER_IMAGE})"
    msg info "  ONEAPP_DOCKER_ARGS:  Additional Docker run arguments"
    msg info "  ONEAPP_AUTO_START:   Auto-start container on boot (yes/no)"
    msg info ""
    msg info "Manual start: /usr/local/bin/docker-phoenix-start.sh"

    return 0
}

# This one is not really mandatory, however it is a handled function
service_cleanup()
{
    msg info "CLEANUP: Stopping Docker services and removing containers"

    # Stop and remove any running containers
    docker stop $(docker ps -q) 2>/dev/null || true
    docker rm $(docker ps -aq) 2>/dev/null || true

    # Stop Docker service
    systemctl stop docker 2>/dev/null || true

    msg info "CLEANUP finished"
}


###############################################################################
###############################################################################
###############################################################################

# Then for modularity purposes you can define your own functions as long as their name
# doesn't clash with the previous functions

#
# Docker helper functions
#

wait_for_docker()
{
    msg info "Waiting for Docker daemon to be ready..."
    local timeout=60
    local count=0

    while ! docker info >/dev/null 2>&1; do
        if [ $count -ge $timeout ]; then
            msg error "Docker daemon failed to start within ${timeout} seconds"
            return 1
        fi
        printf .
        sleep 1
        count=$((count + 1))
    done
    echo
    msg info "Docker daemon is ready"
    return 0
}

is_docker_image_available()
{
    local image="$1"
    docker images "${image}" --format "table {{.Repository}}:{{.Tag}}" | grep -q "${image}"
}

pull_docker_image()
{
    local image="$1"
    msg info "Pulling Docker image: ${image}"

    if ! docker pull "${image}"; then
        msg error "Failed to pull Docker image: ${image}"
        return 1
    fi

    msg info "Successfully pulled Docker image: ${image}"
    return 0
}

run_docker_container()
{
    local image="$1"
    local args="$2"

    msg info "Running Docker container: ${image}"
    msg info "Docker arguments: ${args}"

    # Run the container
    docker run ${args} "${image}"
}
