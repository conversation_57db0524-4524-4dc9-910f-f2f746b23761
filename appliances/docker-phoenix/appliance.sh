#!/usr/bin/env bash
set -o errexit -o pipefail -o nounset

# ------------------------------------------------------------------------------
# Appliance metadata
# ------------------------------------------------------------------------------
ONE_SERVICE_NAME='Phoenix RTOS Docker Appliance'
ONE_SERVICE_VERSION='1.0.0'
ONE_SERVICE_BUILD=$(date +%s)
ONE_SERVICE_SHORT_DESCRIPTION='Ubuntu Jammy VM that auto-runs Phoenix RTOS container'
ONE_SERVICE_RECONFIGURABLE=false

# Embed the Base64 logo here (200×200 PNG):
ONE_SERVICE_ICON64='iVBORw0KGgAAIrCA…<paste entire logo.b64 here>…'

# ------------------------------------------------------------------------------
# Contextualization parameters (OpenNebula "one-context")
# ------------------------------------------------------------------------------
ONE_SERVICE_PARAMS=(
  'PASSWORD'           'configure'  'Password for root user (if used)'     'O|text'
  'SSH_PUBLIC_KEY'     'configure'  'SSH key for root login'               'O|text64'
  'START_SCRIPT'       'configure'  'Script to run at first boot'          'O|text64'
  'GROW_ROOTFS'        'configure'  'Automatically grow root filesystem'   'O|boolean'
  'ONEAPP_DOCKER_IMAGE' 'configure' 'Docker image to run'                  'O|text'
  'ONEAPP_DOCKER_ARGS'  'configure' 'Additional Docker run arguments'      'O|text'
  'ONEAPP_AUTO_START'   'configure' 'Auto-start container on boot'         'O|boolean'
)

# ------------------------------------------------------------------------------
# Context defaults
# ------------------------------------------------------------------------------
PASSWORD="${PASSWORD:-root}"
SSH_PUBLIC_KEY="${SSH_PUBLIC_KEY:-}"
START_SCRIPT="${START_SCRIPT:-}"
GROW_ROOTFS="${GROW_ROOTFS:-YES}"
ONEAPP_DOCKER_IMAGE="${ONEAPP_DOCKER_IMAGE:-pablodelarco/phoenix-rtos-one:latest}"
ONEAPP_DOCKER_ARGS="${ONEAPP_DOCKER_ARGS:---rm -it --network host}"
ONEAPP_AUTO_START="${ONEAPP_AUTO_START:-YES}"

# ------------------------------------------------------------------------------
# Help & NO-OP install (install done by Packer)
# ------------------------------------------------------------------------------
service_help() {
  cat <<EOF >/dev/tty1
Phoenix RTOS Docker Appliance
Stages:
  install    - not used (built into image)
  configure  - runs once at first boot to finalize VM
EOF
}

service_install() {
  # Packer baked everything; nothing to do at install time.
  echo "[Phoenix RTOS] install not required." >/dev/tty1 || true
}

# ------------------------------------------------------------------------------
# Simple spinner on tty1 while "perform" runs in background
# ------------------------------------------------------------------------------
spin() {
  local pid=$1
  local delay=0.1
  local str='|/-\'
  local i=0
  printf "\rStarting Phoenix RTOS... %c" "${str:i:1}" >/dev/tty1
  while kill -0 "$pid" 2>/dev/null; do
    i=$(( (i + 1) % ${#str} ))
    printf "\rStarting Phoenix RTOS... %c" "${str:i:1}" >/dev/tty1
    sleep "$delay"
  done
  printf "\rStarting Phoenix RTOS... done\n" >/dev/tty1
}

# ------------------------------------------------------------------------------
# Configure — runs one-time at first boot
# ------------------------------------------------------------------------------
service_configure() {
  # Prevent re-run
  local SENTINEL=/var/lib/one-context/phoenix-configured
  [[ -e "$SENTINEL" ]] && exit 0
  touch "$SENTINEL"

  # Redirect all verbose logs to /root/startup.log
  exec > /root/startup.log 2>&1

  perform() {
    # 1) Set root password if custom
    if [[ -n "$PASSWORD" && "$PASSWORD" != "root" ]]; then
      echo "root:${PASSWORD}" | chpasswd
    fi

    # 2) Install SSH key if given
    if [[ -n "$SSH_PUBLIC_KEY" ]]; then
      mkdir -p /root/.ssh
      echo "$SSH_PUBLIC_KEY" > /root/.ssh/authorized_keys
      chmod 600 /root/.ssh/authorized_keys
    fi

    # 3) Run any START_SCRIPT if provided (cloud-init style)
    if [[ -n "$START_SCRIPT" ]]; then
      echo "$START_SCRIPT" > /tmp/start_script.sh
      chmod +x /tmp/start_script.sh
      /tmp/start_script.sh || true
    fi

    # 4) Grow root filesystem if requested
    if [[ "$GROW_ROOTFS" =~ ^(yes|YES)$ ]]; then
      growpart /dev/vda 1 || true
      resize2fs /dev/vda1   || true
    fi

    # 5) Hard-code DNS to Google + Cloudflare
    sed -i 's/^#\?DNS=.*/DNS=******* *******/'        /etc/systemd/resolved.conf
    sed -i 's/^#\?FallbackDNS=.*/FallbackDNS=******* *******/' /etc/systemd/resolved.conf
    systemctl restart systemd-resolved

    # 6) Launch your container if not yet running
    docker inspect phoenix-container >/dev/null 2>&1 \
      || docker run -d --name phoenix-container --restart=always --network host \
           "${ONEAPP_DOCKER_IMAGE}"

    # 7) Auto-login on console (tty1)
    mkdir -p /etc/systemd/system/<EMAIL>.d
    cat <<CONF >/etc/systemd/system/<EMAIL>.d/autologin.conf
[Service]
ExecStart=
ExecStart=-/sbin/agetty --autologin root --noclear %I linux
CONF
    systemctl daemon-reload
    <NAME_EMAIL> || true
    <NAME_EMAIL>

    # 8) Drop console straight into the running container
    cat <<'EOF' >> /root/.bash_profile

# On console login, clear screen and exec the Phoenix RTOS container
clear
exec docker run --rm -it --network host "${ONEAPP_DOCKER_IMAGE}"
EOF
  }

  # Run "perform" in background + spinner on tty1
  perform & pid=$!
  spin $pid
  wait $pid
}

# ------------------------------------------------------------------------------
# Dispatch based on argument
# ------------------------------------------------------------------------------
case "${1:-help}" in
  install)   service_install ;;
  configure) service_configure ;;
  help|*)    service_help ;;
esac
