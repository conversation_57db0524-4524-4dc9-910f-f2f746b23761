---
:app:
  :name: openFGS # name used to make the app with the makefile
  :type: service # there are service (complex apps) and distro (base apps)
  :os:
    :type: linux # linux, freebsd or windows
    :base: ubuntu24 # distro where the app runs on
  :hypervisor: KVM
  :context: # which context params are used to control the app
    :prefixed: true # params are prefixed with ONEAPP_
    :params:
      :OPEN5GS_MCC: '999'
      :OPEN5GS_MNC: '75'
      :OPEN5GS_N2_IP: '********'
      :OPEN5GS_N3_IP: '********'
      :OPEN5GS_TAC: '1'
      :OPEN5GS_WEBUI_IP: '0.0.0.0'
      :OPEN5GS_WEBUI_PORT: '3000'

:one:
  # reference template for running the virtual appliance.
  # Must be diskless, with ssh access and accessible by  the frontend
  :template: # how the VM template is expected to be on OpenNebula
    NAME: openFGS
    TEMPLATE:
      ARCH: x86_64
      CONTEXT:
        NETWORK: 'YES'
        SSH_PUBLIC_KEY: "$USER[SSH_PUBLIC_KEY]"
      CPU: '4'
      CPU_MODEL:
        MODEL: host-passthrough
      GRAPHICS:
        LISTEN: 0.0.0.0
        TYPE: vnc
      MEMORY: '16384'
      NIC:
        NETWORK: service
      NIC_DEFAULT:
        MODEL: virtio
  :datastore_name: default # target datatore to import the one-apps produced image
  :timeout: '90' # timeout for XMLRPC calls

:infra:
  :disk_format: qcow2 # oneapps built image disk format
  :apps_path: /var/tmp # directory where one-apps exports the appliances to
