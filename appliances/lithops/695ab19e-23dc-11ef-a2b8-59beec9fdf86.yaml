---
name: lithops
version: 6.10.0-3-********
publisher: OpenNebula Systems
description: |-
  Appliance with preinstalled [Lithops](https://lithops-cloud.github.io/docs/) and Docker.

  See the dedicated [documentation](https://github.com/OpenNebula/marketplace-community/wiki/lithops_quick).
short_description: Appliance with preinstalled Lithops for KVM hosts
tags:
  - lithops
  - ubuntu
  - service
format: qcow2
creation_time: 1747149743
os-id: Ubuntu
os-release: 22.04 LTS
os-arch: x86_64
hypervisor: KVM
opennebula_version: 6.2, 6.3, 6.4, 6.6, 6.8, 6.10, 7.0
opennebula_template:
  context:
    network: 'YES'
    oneapp_lithops_backend: "$ONEAPP_LITHOPS_BACKEND"
    oneapp_lithops_storage: "$ONEAPP_LITHOPS_STORAGE"
    oneapp_minio_endpoint: "$ONEAPP_MINIO_ENDPOINT"
    oneapp_minio_access_key_id: "$ONEAPP_MINIO_ACCESS_KEY_ID"
    oneapp_minio_secret_access_key: "$ONEAPP_MINIO_SECRET_ACCESS_KEY"
    oneapp_minio_bucket: "$ONEAPP_MINIO_BUCKET"
    oneapp_minio_endpoint_cert: "$ONEAPP_MINIO_ENDPOINT_CERT"
    ssh_public_key: "$USER[SSH_PUBLIC_KEY]"
  cpu: '1'
  graphics:
    listen: 0.0.0.0
    type: vnc
  inputs_order: >-
    ONEAPP_LITHOPS_BACKEND,ONEAPP_LITHOPS_STORAGE,ONEAPP_MINIO_ENDPOINT,ONEAPP_MINIO_ACCESS_KEY_ID,ONEAPP_MINIO_SECRET_ACCESS_KEY,ONEAPP_MINIO_BUCKET,ONEAPP_MINIO_ENDPOINT_CERT
  memory: '768'
  os:
    arch: x86_64
  user_inputs:
    oneapp_lithops_backend: O|text|Compute backend| |localhost
    oneapp_lithops_storage: O|text|Storage backend| |localhost
    oneapp_minio_endpoint: O|text|MinIO endpoint URL| |
    oneapp_minio_access_key_id: O|text|MinIO account user access key| |
    oneapp_minio_secret_access_key: O|text|MinIO account user secret access key| |
    oneapp_minio_bucket: O|text|MinIO bucket name| |
    oneapp_minio_endpoint_cert: O|text64|CA certificate for MinIO connection| |
logo: lithops.png
images:
  - name: lithops
    url: >-
      https://d38nm155miqkyg.cloudfront.net/lithops-6.10.0-3-********.qcow2
    type: OS
    dev_prefix: vd
    driver: qcow2
    size: **********
    checksum:
      md5: 5a2836f94361200f4a74e36cf56b7429
      sha256: 90de590ea070c33809924ad85bfab59e299a50328a2c19c89bb937569d8e65d2
