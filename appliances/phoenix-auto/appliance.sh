#!/usr/bin/env bash
set -o errexit -o pipefail -o nounset

# Appliance metadata
ONE_SERVICE_NAME='pablodelarco/phoenix-rtos-one:latest VM'
ONE_SERVICE_VERSION='1.0.0'
ONE_SERVICE_BUILD=$(date +%s)
ONE_SERVICE_SHORT_DESCRIPTION='Ubuntu VM running pablodelarco/phoenix-rtos-one:latest'
ONE_SERVICE_RECONFIGURABLE=false

# Context parameters
ONE_SERVICE_PARAMS=(
  'PASSWORD'       'configure'  'Password for root user'               'O|text'
  'SSH_PUBLIC_KEY' 'configure'  'SSH key for root login'               'O|text64'
  'GROW_ROOTFS'    'configure'  'Automatically grow root filesystem'   'O|boolean'
)

# Defaults
PASSWORD="${PASSWORD:-root}"
SSH_PUBLIC_KEY="${SSH_PUBLIC_KEY:-}"
GROW_ROOTFS="${GROW_ROOTFS:-YES}"

service_help() {
  cat <<EOF >/dev/tty1
pablodelarco/phoenix-rtos-one:latest VM Appliance
Stages:
  install    - not used (built into image)
  configure  - runs once at first boot
EOF
}

service_install() {
  echo "[Docker VM] install not required." >/dev/tty1 || true
}

service_configure() {
  local SENTINEL=/var/lib/one-context/docker-vm-configured
  [[ -e "$SENTINEL" ]] && exit 0
  touch "$SENTINEL"

  exec > /root/startup.log 2>&1

  perform() {
    # Set root password
    if [[ -n "$PASSWORD" && "$PASSWORD" != "root" ]]; then
      echo "root:${PASSWORD}" | chpasswd
    fi

    # Install SSH key
    if [[ -n "$SSH_PUBLIC_KEY" ]]; then
      mkdir -p /root/.ssh
      echo "$SSH_PUBLIC_KEY" > /root/.ssh/authorized_keys
      chmod 600 /root/.ssh/authorized_keys
    fi

    # Grow filesystem
    if [[ "$GROW_ROOTFS" =~ ^(yes|YES)$ ]]; then
      growpart /dev/vda 1 || true
      resize2fs /dev/vda1   || true
    fi

    # Configure DNS
    sed -i 's/^#\?DNS=.*/DNS=8.8.8.8 *******/' /etc/systemd/resolved.conf
    sed -i 's/^#\?FallbackDNS=.*/FallbackDNS=******* *******/' /etc/systemd/resolved.conf
    systemctl restart systemd-resolved

    # Install Docker if not present
    if ! command -v docker >/dev/null 2>&1; then
      echo "Installing Docker..."
      DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends docker-ce docker-ce-cli containerd.io
      systemctl enable --now docker
    else
      systemctl start docker
    fi

    # Create startup script
    cat <<'SCRIPT' > /usr/local/bin/docker-startup.sh
#!/bin/bash
set -e

# Wait for Docker
while ! docker info >/dev/null 2>&1; do
  echo 'Waiting for Docker...'
  sleep 2
done

# Pull and run the container
echo 'Starting pablodelarco/phoenix-rtos-one:latest...'
exec docker run --rm -it --network host pablodelarco/phoenix-rtos-one:latest
SCRIPT

    chmod +x /usr/local/bin/docker-startup.sh

    # Configure auto-exec on console
    cat <<'EOF' >> /root/.bash_profile

# Auto-start Docker container on console login
if [ "$(tty)" = "/dev/tty1" ]; then
  clear
  exec /usr/local/bin/docker-startup.sh
fi
EOF
  }

  perform & pid=$!
  
  # Simple spinner
  while kill -0 "$pid" 2>/dev/null; do
    printf "\rConfiguring Docker VM... " >/dev/tty1
    sleep 1
  done
  printf "\rDocker VM ready!        \n" >/dev/tty1
  
  wait $pid
}

case "${1:-help}" in
  install)   service_install ;;
  configure) service_configure ;;
  help|*)    service_help ;;
esac
