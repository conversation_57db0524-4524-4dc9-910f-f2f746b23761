---
name: Lithops - Client
version: 6.10.0-3-20250513
publisher: OpenNebula Systems
description: |-
  Appliance with preinstalled [Lithops](https://lithops-cloud.github.io/docs/).

  See the dedicated [documentation](https://github.com/OpenNebula/marketplace-community/wiki/lithops_intro).
short_description: Appliance with preinstalled Lithops for KVM hosts
tags:
- lithops
- big-data
- analytics
- ubuntu
- service
type: VMTEMPLATE
format: qcow2
creation_time: 1747149743
os-id: Ubuntu
os-release: '22.04 LTS'
os-arch: x86_64
hypervisor: KVM
opennebula_version: 6.8, 6.10, 7.0
opennebula_template:
  context:
    fallback_dns: '$FALLBACK_DNS'
    fallback_gw: '$FALLBACK_GW'
    network: 'YES'
    report_ready: 'YES'
    oneapp_lithops_backend: '$ONEAPP_LITHOPS_BACKEND'
    oneapp_lithops_standalone: '$ONEAPP_LITHOPS_STANDALONE'
    oneapp_lithops_storage: '$ONEAPP_LITHOPS_STORAGE'
    oneapp_minio_access_key_id: '$ONEAPP_MINIO_ACCESS_KEY_ID'
    oneapp_minio_bucket: '$ONEAPP_MINIO_BUCKET'
    oneapp_minio_endpoint: '$ONEAPP_MINIO_ENDPOINT'
    oneapp_minio_endpoint_cert: '$ONEAPP_MINIO_ENDPOINT_CERT'
    oneapp_minio_secret_access_key: '$ONEAPP_MINIO_SECRET_ACCESS_KEY'
    oneapp_onebe_autoscale: '$ONEAPP_ONEBE_AUTOSCALE'
    oneapp_onebe_docker_password: '$ONEAPP_ONEBE_DOCKER_PASSWORD'
    oneapp_onebe_docker_user: '$ONEAPP_ONEBE_DOCKER_USER'
    oneapp_onebe_runtime_cpu: '$ONEAPP_ONEBE_RUNTIME_CPU'
    oneapp_onebe_runtime_memory: '$ONEAPP_ONEBE_RUNTIME_MEMORY'
    oneapp_amqp_user: '$ONEAPP_AMQP_USER'
    oneapp_amqp_pass: '$ONEAPP_AMQP_PASS'
    oneapp_amqp_host: '$ONEAPP_AMQP_HOST'
    oneapp_amqp_port: '$ONEAPP_AMQP_PORT'
    oneapp_onebe_worker_processes: '$ONEAPP_ONEBE_WORKER_PROCESSES'
    oneapp_onebe_runtime_timeout: '$ONEAPP_ONEBE_RUNTIME_TIMEOUT'
    token: 'YES'
    start_script_base64: "bWtkaXIgL21udC9jb250ZXh0Cm1vdW50IC9kZXYvY2Ryb20gL21udC9jb250ZXh0"
    ssh_public_key: "$USER[SSH_PUBLIC_KEY]"
  cpu: '1'
  graphics:
    listen: 0.0.0.0
    type: vnc
  inputs_order: >-
    ONEAPP_LITHOPS_BACKEND,ONEAPP_LITHOPS_STORAGE,ONEAPP_MINIO_ENDPOINT,ONEAPP_MINIO_ACCESS_KEY_ID,ONEAPP_MINIO_SECRET_ACCESS_KEY,ONEAPP_MINIO_BUCKET,ONEAPP_MINIO_ENDPOINT_CERT,ONEAPP_ONEBE_KUBECFG_PATH,ONEAPP_ONEBE_DOCKER_USER,ONEAPP_ONEBE_DOCKER_PASSWORD,ONEAPP_ONEBE_AUTOSCALE
  memory: '768'
  os:
    arch: x86_64
  logo: images/logos/lithops.png
  user_inputs:
    oneapp_lithops_standalone: "O|boolean|Appliance runs as standalone or as a service| |no"
    oneapp_lithops_backend: "O|text|Compute backend| |localhost"
    oneapp_lithops_storage: "O|text|Storage backend| |localhost"
    oneapp_minio_endpoint: "O|text|MinIO endpoint URL| |"
    oneapp_minio_access_key_id: "O|text|MinIO account user access key| |"
    oneapp_minio_secret_access_key: "O|text|MinIO account user secret access key| |"
    oneapp_minio_bucket: "O|text|MinIO bucket name| |"
    oneapp_minio_endpoint_cert: "O|text64|CA certificate for MinIO connection| |"
    oneapp_onebe_docker_user: "O|text|Docker user for Lithops||"
    oneapp_onebe_docker_password: "O|password|Docker user password for Lithops||"
    oneapp_amqp_user: "O|text|User for AMQP RabbitMQ||"
    oneapp_amqp_pass: "O|password|Password for AMQP RabbitMQ||"
    oneapp_amqp_host: "O|text|Host for AMQP RabbitMQ||"
    oneapp_amqp_port: "O|text|Port for AMQP RabbitMQ||"
    oneapp_onebe_autoscale: "0|text|Lithops backend autoscale option||all"
logo: lithops.png
disks:
- "Lithops"
