---
name: Lithops Service
version: 6.10.0-3-20250513
publisher: OpenNebula Systems
description: |-
  Lithops Service deployment using RabbitMQ as message broker, Lithops Workers as compute backend and Min<PERSON> as storage backend,
  orchestrated by [OneFlow](https://docs.opennebula.io/stable/management_and_operations/multivm_service_management/appflow_elasticity.html)

  Requires
  [OneFlow](https://docs.opennebula.io/stable/management_and_operations/multivm_service_management/overview.html)
  and
  [OneGate](https://docs.opennebula.io/stable/management_and_operations/multivm_service_management/onegate_usage.html)
  OpenNebula components.

  See the dedicated [documentation](https://github.com/OpenNebula/marketplace-community/wiki/lithops-service).

  Based on VM templates
  - [Lithops - Client](https://community-marketplace.opennebula.io/appliance/01076f79-94be-4c8f-b57f-dec96d5d15a4.yaml)
  - [Lithops - Worker](https://community-marketplace.opennebula.io/appliance/8628d1b9-0868-469b-9edc-2384a898dddf.yaml)
  - [Service Virtual Router](https://marketplace.opennebula.io/appliance/cc96d537-f6c7-499f-83f1-15ac4058750e)
  - [MinIO Multi-Node](32450c33-3e40-41a3-a9df-03a3f0a614c1)
  - [RabbitMQ](https://community-marketplace.opennebula.io/appliance/c16c278c-464e-4b34-a77b-47208179dc76)
short_description: Lithops deployment for KVM hosts, orchestrated by OneFlow
tags:
- lithops
- computing
- big-data
- analytics
- ubuntu
- service
creation_time: 1747149743
os-id: Ubuntu
os-release: '22.04 LTS'
os-arch: x86_64
hypervisor: KVM
opennebula_version: 6.8, 6.10, 7.0
type: 'SERVICE_TEMPLATE'
roles:
  vr: 'Lithops - Virtual Router'
  minio: 'Lithops - MinIO'
  lithops_client: 'Lithops - Client'
  lithops_worker: 'Lithops - Worker'
  rabbitmq: 'RabbitMQ'
opennebula_template:
  name: Service Lithops
  deployment: straight
  description: ''
  roles:
  - name: vr
    cardinality: 1
    min_vms: 1
    vm_template_contents: |
      NIC = [
        NAME = "NIC0",
        NETWORK_ID = "$Public"
      ]
      NIC = [
        NAME = "NIC1",
        NETWORK_ID = "$Private"
      ]
      ONEAPP_VNF_DNS_ENABLED = "$ONEAPP_VNF_DNS_ENABLED"
      ONEAPP_VNF_DNS_INTERFACES = "$ONEAPP_VNF_DNS_INTERFACES"
      ONEAPP_VNF_DNS_NAMESERVERS = "$ONEAPP_VNF_DNS_NAMESERVERS"
      ONEAPP_VNF_NAT4_ENABLED = "$ONEAPP_VNF_NAT4_ENABLED"
      ONEAPP_VNF_NAT4_INTERFACES_OUT = "$ONEAPP_VNF_NAT4_INTERFACES_OUT"
      ONEAPP_VNF_ROUTER4_ENABLED = "$ONEAPP_VNF_ROUTER4_ENABLED"
      ONEAPP_VNF_ROUTER4_INTERFACES = "$ONEAPP_VNF_ROUTER4_INTERFACES"
    cooldown: 120
    elasticity_policies: []
    scheduled_policies: []
  - name: rabbitmq
    parents:
    - vr
    cardinality: 1
    min_vms: 1
    vm_template_contents: |
      NIC = [
        NAME = "NIC0",
        NETWORK_ID = "$Private"
      ]
      FALLBACK_GW = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      FALLBACK_DNS = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      ONEAPP_RABBITMQ_USER = "$ONEAPP_RABBITMQ_USER"
      ONEAPP_RABBITMQ_PASS = "$ONEAPP_RABBITMQ_PASS"
    cooldown: 120
    elasticity_policies: []
    scheduled_policies: []
  - name: lithops_worker
    parents:
    - vr
    - rabbitmq
    cardinality: 1
    vm_template_contents: |
      NIC = [
        NAME = "NIC0",
        NETWORK_ID = "$Private"
      ]
      FALLBACK_GW = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      FALLBACK_DNS = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      ONEAPP_AMQP_USER = "$ONEAPP_RABBITMQ_USER"
      ONEAPP_AMQP_PASS = "$ONEAPP_RABBITMQ_PASS"
      ONEAPP_AMQP_HOST = "${rabbitmq.TEMPLATE.CONTEXT.ETH0_IP}"
      ONEAPP_AMQP_PORT = "5672"
    cooldown: 120
    elasticity_policies: []
    scheduled_policies: []
  - name: minio
    parents:
    - vr
    cardinality: 1
    vm_template_contents: |
      NIC = [
        NAME = "NIC0",
        NETWORK_ID = "$Private"
      ]
      FALLBACK_GW = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      FALLBACK_DNS = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      ONEAPP_MINIO_ROOT_USER="$ONEAPP_MINIO_ROOT_USER"
      ONEAPP_MINIO_ROOT_PASSWORD="$ONEAPP_MINIO_ROOT_PASSWORD"
      ONEAPP_MINIO_HOSTNAME="minio.opennebula.local"
      ONEAPP_MINIO_TLS_ENABLED="NO"
    cooldown: 120
    elasticity_policies: []
    scheduled_policies: []
  - name: lithops_client
    parents:
    - vr
    - rabbitmq
    - minio
    - lithops_worker
    cardinality: 1
    vm_template_contents: |
      NIC = [
        NAME = "NIC0",
        NETWORK_ID = "$Private"
      ]
      FALLBACK_GW = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      FALLBACK_DNS = "${vr.TEMPLATE.CONTEXT.ETH1_IP}"
      ONEAPP_LITHOPS_BACKEND="amqp"
      ONEAPP_LITHOPS_STORAGE="minio"
      ONEAPP_MINIO_ACCESS_KEY_ID="$ONEAPP_MINIO_ROOT_USER"
      ONEAPP_MINIO_SECRET_ACCESS_KEY="$ONEAPP_MINIO_ROOT_PASSWORD"
      ONEAPP_ONEBE_DOCKER_USER="$ONEAPP_ONEBE_DOCKER_USER"
      ONEAPP_ONEBE_DOCKER_PASSWORD="$ONEAPP_ONEBE_DOCKER_PASSWORD"
      ONEAPP_ONEBE_AUTOSCALE="$ONEAPP_ONEBE_AUTOSCALE"
      ONEAPP_ONEBE_RUNTIME_CPU="${lithops_worker.TEMPLATE.CPU}"
      ONEAPP_ONEBE_RUNTIME_MEMORY="${lithops_worker.TEMPLATE.MEMORY}"
      ONEAPP_AMQP_USER="$ONEAPP_RABBITMQ_USER"
      ONEAPP_AMQP_PASS="$ONEAPP_RABBITMQ_PASS"
      ONEAPP_AMQP_HOST="${rabbitmq.TEMPLATE.CONTEXT.ETH0_IP}"
      ONEAPP_AMQP_PORT="5672"
      ONEAPP_MINIO_ENDPOINT="$ONEAPP_MINIO_ENDPOINT"
      ONEAPP_MINIO_ACCESS_KEY_ID="$ONEAPP_MINIO_ACCESS_KEY_ID"
      ONEAPP_MINIO_SECRET_ACCESS_KEY="$ONEAPP_MINIO_SECRET_ACCESS_KEY"
      ONEAPP_ONEBE_WORKER_PROCESSES="${lithops_worker.TEMPLATE.CPU}"
      ONEAPP_ONEBE_RUNTIME_TIMEOUT="600"
    cooldown: 120
    elasticity_policies: []
    scheduled_policies: []
  networks:
    Public: 'M|network|Public| |id::'
    Private: 'M|network|Private| |id::'
  custom_attrs:
    ONEAPP_VNF_DNS_ENABLED: '0|boolean|Enable DNS recursor||YES'
    ONEAPP_VNF_DNS_INTERFACES: '0|text|DNS - Interfaces||eth1'
    ONEAPP_VNF_DNS_NAMESERVERS: '0|text|DNS - Nameservers||*******,*******'
    ONEAPP_VNF_NAT4_ENABLED: '0|boolean|Enable NAT||YES'
    ONEAPP_VNF_NAT4_INTERFACES_OUT: '0|text|NAT - Outgoing Interfaces||eth0'
    ONEAPP_VNF_ROUTER4_ENABLED: '0|boolean|Enable Router||YES'
    ONEAPP_VNF_ROUTER4_INTERFACES: '0|text|Router - Interfaces||eth0,eth1'
    ONEAPP_MINIO_ROOT_USER: 'M|text|MinIO Root User||minioadmin'
    ONEAPP_MINIO_ROOT_PASSWORD: 'M|password|MinIO Root User Password||'
    ONEAPP_ONEBE_AUTOSCALE: '0|text|Lithops backend autoscale option||all'
    ONEAPP_RABBITMQ_USER: 'M|text|RabbitMQ User||rabbitadmin'
    ONEAPP_RABBITMQ_PASS: 'M|password|RabbitMQ User Password||'
    ONEAPP_MINIO_ENDPOINT: 'M|text|Lithops storage backend MinIO endpoint URL||http://${minio.TEMPLATE.CONTEXT.ETH0_IP}:9000'
    ONEAPP_MINIO_ACCESS_KEY_ID: 'M|text|Lithops storage backend MinIO account user access key||$ONEAPP_MINIO_ROOT_USER'
    ONEAPP_MINIO_SECRET_ACCESS_KEY: 'M|password|Lithops storage backend MinIO account user secret access key||$ONEAPP_MINIO_ROOT_PASSWORD'
  ready_status_gate: true
logo: lithops.png
images: []
