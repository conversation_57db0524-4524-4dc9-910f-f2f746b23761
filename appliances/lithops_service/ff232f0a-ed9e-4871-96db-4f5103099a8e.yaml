---
name: <PERSON>th<PERSON> - Virtual Router
version: 6.10.0-2-********
publisher: OpenNebula Systems
description: |-
  Appliance providing several Virtual Network Functions (routing, NAT, DNS, DHCP)
  to run as regular VM.

  See the dedicated [documentation](https://github.com/OpenNebula/one-apps/wiki).
short_description: Appliance with several Virtual Network Functions to run
  as VM
tags:
- lithops
- vnf
- vrouter
- service
- alpine
type: VMTEMPLATE
format: qcow2
creation_time: **********
os-id: Alpine
os-release: '3.18'
os-arch: x86_64
hypervisor: ALL
opennebula_version: 6.8, 6.10, 7.0
opennebula_template:
  context:
    service_id: "$SERVICE_ID"
    network: 'YES'
    token: 'YES'
    report_ready: 'YES'
    ssh_public_key: "$USER[SSH_PUBLIC_KEY]"
    oneapp_vnf_dhcp4_enabled: "$ONEAPP_VNF_DHCP4_ENABLED"
    oneapp_vnf_dhcp4_interfaces: "$ONEAPP_VNF_DHCP4_INTERFACES"
    oneapp_vnf_dhcp4_lease_time: "$ONEAPP_VNF_DHCP4_LEASE_TIME"
    oneapp_vnf_dns_enabled: "$ONEAPP_VNF_DNS_ENABLED"
    oneapp_vnf_dns_interfaces: "$ONEAPP_VNF_DNS_INTERFACES"
    oneapp_vnf_dns_max_cache_ttl: "$ONEAPP_VNF_DNS_MAX_CACHE_TTL"
    oneapp_vnf_dns_use_rootservers: "$ONEAPP_VNF_DNS_USE_ROOTSERVERS"
    oneapp_vnf_haproxy_enabled: 'YES'
    oneapp_vnf_haproxy_onegate_enabled: 'YES'
    oneapp_vnf_haproxy_lb0_ip: "<ETH0_EP0>"
    oneapp_vnf_haproxy_lb0_port: "$ONEAPP_VNF_HAPROXY_LB0_PORT"
    oneapp_vnf_haproxy_lb1_ip: "<ETH0_EP0>"
    oneapp_vnf_haproxy_lb1_port: "$ONEAPP_VNF_HAPROXY_LB1_PORT"
    oneapp_vnf_haproxy_interfaces: "$ONEAPP_VNF_HAPROXY_INTERFACES"
    oneapp_vnf_lb_enabled: "$ONEAPP_VNF_LB_ENABLED"
    oneapp_vnf_lb_interfaces: "$ONEAPP_VNF_LB_INTERFACES"
    oneapp_vnf_nat4_enabled: "$ONEAPP_VNF_NAT4_ENABLED"
    oneapp_vnf_nat4_interfaces_out: "$ONEAPP_VNF_NAT4_INTERFACES_OUT"
    oneapp_vnf_router4_enabled: 'YES'
    oneapp_vnf_sdnat4_enabled: "$ONEAPP_VNF_SDNAT4_ENABLED"
    oneapp_vnf_sdnat4_interfaces: "$ONEAPP_VNF_SDNAT4_INTERFACES"
    oneapp_vnf_sdnat4_onegate_enabled: 'YES'
  cpu: '1'
  graphics:
    listen: 0.0.0.0
    type: vnc
  inputs_order: ''
  memory: '512'
  nic_default:
    model: virtio
  lxd_security_privileged: 'true'
  os:
    arch: x86_64
  user_inputs: {}
logo: router.png
disks:
- "Service Virtual Router"
