---
name: Lithops - MinIO
version: 6.10.0-3-20250127
publisher: OpenNebula Systems
description: |-
  Appliance with preinstalled [MinIO](https://min.io/docs/minio/linux/index.html).

  See the dedicated [documentation](https://github.com/OpenNebula/one-apps/wiki/minio_quick).
short_description: Appliance with preinstalled MinIO server for KVM hosts
tags:
- minio
- storage
- ubuntu
- service
format: qcow2
creation_time: 1728651992
os-id: Ubuntu
os-release: 22.04 LTS
os-arch: x86_64
hypervisor: KVM
opennebula_version: 6.4, 6.8, 6.10, 7.0
opennebula_template:
  context:
    fallback_gw: "$FALLBACK_GW"
    fallback_dns: "$FALLBACK_DNS"
    network: 'YES'
    report_ready: 'YES'
    oneapp_minio_hostname: "$ONEAPP_MINIO_HOSTNAME"
    oneapp_minio_multi: 'YES'
    oneapp_minio_opts: "$ONEAPP_MINIO_OPTS"
    oneapp_minio_root_password: "$ONEAPP_MINIO_ROOT_PASSWORD"
    oneapp_minio_root_user: "$ONEAPP_MINIO_ROOT_USER"
    oneapp_minio_tls_enabled: "$ONEAPP_MINIO_TLS_ENABLED"
    oneapp_minio_tls_cert: "$ONEAPP_MINIO_TLS_CERT"
    oneapp_minio_tls_key: "$ONEAPP_MINIO_TLS_KEY"
    token: 'YES'
    ssh_public_key: "$USER[SSH_PUBLIC_KEY]"
  cpu: '1'
  graphics:
    listen: 0.0.0.0
    type: vnc
  inputs_order: >-
    ONEAPP_MINIO_HOSTNAME,ONEAPP_MINIO_ROOT_USER,ONEAPP_MINIO_ROOT_PASSWORD,ONEAPP_MINIO_OPTS,ONEAPP_MINIO_TLS_ENABLED,ONEAPP_MINIO_TLS_CERT,ONEAPP_MINIO_TLS_KEY
  memory: '2048'
  os:
    arch: x86_64
  logo: images/logos/minio.png
  user_inputs:
    oneapp_minio_hostname: >-
      O|text|MinIO hostname for TLS certificate| |localhost,minio-*.example.net
    oneapp_minio_opts: >-
      O|text|Additional commandline options for MinIO server| |--console-address
      :9001
    oneapp_minio_root_password: O|password|Mv binIO root user password for
      MinIO server
    oneapp_minio_root_user: O|text|MinIO root user for MinIO server| |myminioadmin
    oneapp_minio_tls_enabled: M|boolean|Enable TLS configuration| |YES
    oneapp_minio_tls_cert: O|text64|MinIO TLS certificate (.crt)| |
    oneapp_minio_tls_key: O|text64|MinIO TLS key (.key)| |
logo: minio.png
images:
- name: service_MinIO
  url: >-
    https://d24fmfybwxpuhu.cloudfront.net/service_MinIO-6.10.0-3-20250127.qcow2
  type: OS
  dev_prefix: vd
  driver: qcow2
  size: 2361393152
  checksum:
    md5: 529dd5a97d447294af6817fe51d0757e
    sha256: cc677ba32dd974d3cd3e2736d927a71b7cb5fce8b3ae90448bc6d6d7b34ca848
